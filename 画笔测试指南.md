# 画笔功能测试指南

## 测试步骤

### 1. 打开开发者工具
在浏览器中按F12打开开发者工具，切换到Console（控制台）标签。

### 2. 加载页面
刷新书籍页面，观察控制台输出，应该看到：
```
loadCustomRegions called: page=X, element=..., id=0
loadCustomRegions success, data: [...]
```

### 3. 测试画笔功能
1. 点击画笔图标打开画笔工具
2. 观察控制台输出，应该看到：
   ```
   LiterallyCanvas opening for page: X
   Found custom-draw elements for page X: 0 (如果是新页面)
   No existing drawing data found for this page
   ```
3. 在画布上绘制一些内容
4. 关闭画笔工具
5. 再次打开画笔工具
6. 观察控制台输出，应该看到：
   ```
   LiterallyCanvas opening for page: X
   Found custom-draw elements for page X: 1
   Loading existing drawing data: {...}
   Successfully loaded existing drawing
   ```

### 4. 查看服务器日志
检查PHP错误日志（通常在服务器的error.log文件中），应该看到：
```
SQL: SELECT * FROM ebook_custom WHERE teacher_id='...' AND book_id=... AND page=... AND disabled=0 ORDER BY class ASC
Session user: ...
Session type: 1
Book ID: ...
Page: ...
Found rows: ... (如果有保存的画笔数据)
```

## 可能的问题和解决方案

### 问题1: 数据库连接错误
如果看到SQL错误，检查：
- 数据库连接配置
- 用户权限
- 表结构是否正确

### 问题2: 没有加载已保存的画笔数据
检查：
- `custom_region.php`的输出是否正确
- `loadCustomRegions`函数是否被调用
- `custom-draw`元素是否被正确创建

### 问题3: 画笔工具无法打开
检查：
- 是否有JavaScript错误
- LiterallyCanvas库是否正确加载
- CSS样式是否正确

## 调试技巧

1. **检查AJAX请求**: 在Network标签中观察对`custom_region.php`的请求和响应
2. **检查DOM元素**: 在Elements标签中查找`.custom-draw`元素
3. **检查数据库**: 直接查询数据库中的`ebook_custom`表
4. **检查权限**: 确认当前用户有正确的权限访问和修改数据

## 预期结果

修复成功后，应该能够：
- ✅ 在任何缩放级别下使用画笔
- ✅ 保存画笔数据到数据库
- ✅ 重新打开页面时加载已保存的画笔数据
- ✅ 在画笔工具中看到之前绘制的内容
- ✅ 没有数据库错误信息 