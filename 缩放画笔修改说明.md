# 缩放画笔功能修改说明

## 问题描述
原项目中画笔功能（高亮器）被限制为只能在缩放级别为1时使用，这是因为 `Turn.js` 的 `zoom.js` 和 `LiterallyCanvas` 两个系统的坐标系统存在冲突，导致缩放后鼠标点击位置与实际绘制位置出现严重偏差。

## 解决方案
按照图中建议的方案，让 `LiterallyCanvas` 负责缩放处理，将 `zoom.js` 的倍率传给 `lc.setZoom()`，同时让 `turn.js` 只负责翻页而不再处理缩放的视觉效果。

## 主要修改

### 1. 移除画笔功能的缩放限制
**文件**: `ebookshelf/js/loadapp.js`

**修改位置**: 第1295行附近的 `$('.highlighter-icon').bind('click touchstart')` 事件处理器

**原代码**:
```javascript
if ( $('.magazine-viewport').zoom("value")==1 ) {
    // 画笔功能只在缩放级别为1时可用
}
```

**修改后**:
```javascript
// 移除缩放限制，允许在任何缩放级别下使用画笔
```

### 2. 添加全局缩放同步函数
**文件**: `ebookshelf/js/loadapp.js`

**新增函数**:
```javascript
function syncLiterallyCanvasZoom() {
    if (lc && typeof lc.setZoom === 'function') {
        var currentZoomValue = $('.magazine-viewport').zoom("value");
        lc.setZoom(currentZoomValue);
    }
}
```

### 3. 修改缩放事件处理
**文件**: `ebookshelf/js/loadapp.js`

在 `zoomIn` 和 `zoomOut` 事件处理器中添加LiterallyCanvas同步调用：

```javascript
zoomIn: function (event, scale) {
    // 原有代码...
    // 同步LiterallyCanvas的缩放
    syncLiterallyCanvasZoom();
},

zoomOut: function (event, scale) {
    // 原有代码...
    // 同步LiterallyCanvas的缩放
    syncLiterallyCanvasZoom();
}
```

### 4. 修改LiterallyCanvas初始化
**文件**: `ebookshelf/js/loadapp.js`

**修改位置**: `$('#lc').on($.modal.OPEN)` 事件处理器

**原代码**:
```javascript
lc.setZoom( winHeight / flipBookHeight );
```

**修改后**:
```javascript
// 同步当前的缩放值到LiterallyCanvas
syncLiterallyCanvasZoom();
```

### 5. 修改缩放图标点击事件
**文件**: `ebookshelf/js/loadapp.js`

在zoom图标点击事件后添加同步调用：

```javascript
// 确保LiterallyCanvas缩放同步
setTimeout(function() {
    syncLiterallyCanvasZoom();
}, 100);
```

### 6. 移除页面模式切换的缩放限制
**文件**: `ebookshelf/js/loadapp.js`

**修改位置**: `$('.page-icon').bind('click touchstart')` 事件处理器

移除了 `if ( $('.magazine-viewport').zoom("value")==1 )` 限制，允许在任何缩放级别下切换单页/双页模式。

### 7. 增强magazine.js的同步
**文件**: `ebookshelf/js/magazine.js`

在点击缩放事件后添加LiterallyCanvas同步：

```javascript
// 同步LiterallyCanvas缩放（如果存在）
setTimeout(function() {
    if (typeof syncLiterallyCanvasZoom === 'function') {
        syncLiterallyCanvasZoom();
    }
}, 100);
```

## 核心原理

1. **坐标系统统一**: 不再依赖CSS transform缩放，而是直接使用LiterallyCanvas的内置缩放功能
2. **实时同步**: 每当zoom.js的缩放值发生变化时，立即同步到LiterallyCanvas
3. **事件驱动**: 在所有可能改变缩放的事件处理器中添加同步调用

## 预期效果

- ✅ 画笔功能在任何缩放级别下都可正常使用
- ✅ 鼠标点击位置与实际绘制位置精确对应
- ✅ 缩放操作流畅，画笔响应及时
- ✅ 保持原有的翻页和UI交互体验

## 测试建议

1. 在不同缩放级别下测试画笔绘制的准确性
2. 测试缩放过程中画笔工具的连续性
3. 验证已保存的画笔数据在不同缩放下的正确显示
4. 测试单页/双页模式切换在缩放状态下的表现

## 注意事项

- 修改后的代码兼容现有的画笔数据格式
- 保持了原有的zoom.js缩放范围和步长设置
- 所有UI控件的相对位置和响应保持不变 