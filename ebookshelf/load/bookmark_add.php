<?php

// error_reporting(E_ALL);
// ini_set('display_errors', 1);

session_start();
if(isset($_SESSION['user_session'])) {
	
	if(isset($_POST['book_id']) && isset($_POST['pages'])) {
	 
		require_once '../inc/settings.inc.php';
		require_once '../inc/functions.inc.php';
		
		$book_id = trim($_POST['book_id']);
		$pages = trim($_POST['pages']);

		openConnection();

		if ( $_SESSION['type'] == 1 ) {
			$SQL = "DELETE FROM ebook_bookmark WHERE teacher_id='".$_SESSION['user_session']."' AND book_id=$book_id ";
		} else {
			$SQL = "DELETE FROM student_bookmark WHERE student_id='".$_SESSION['user_session']."' AND book_id=$book_id ";
		}
		$objConn->query($SQL);
		
		if ( $pages != '' ) {
			
			if ( $_SESSION['type'] == 1 ) {
				$SQL = "INSERT INTO ebook_bookmark (teacher_id,book_id,pages,disabled,edit_user,edit_date) VALUES ('".$_SESSION['user_session']."','$book_id','$pages','0','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."')";
			} else {
				$SQL = "INSERT INTO student_bookmark (student_id,book_id,pages,disabled,edit_user,edit_date) VALUES ('".$_SESSION['user_session']."','$book_id','$pages','0','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."')";
			}
			$objConn->query($SQL);
			
			if ( $_SESSION['type'] == 1 ) {
				$SQLOG = "INSERT INTO ebook_log (log,book_id,teacher_id,edit_date) VALUES ";
				$SQLOG .= "('update bookmark successfully.','".$book_id."','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."')";
			} else {
				$SQLOG = "INSERT INTO student_log (log,book_id,student_id,edit_date) VALUES ";
				$SQLOG .= "('update bookmark successfully.','".$book_id."','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."')";				
			}
			$objConn->query($SQLOG);

			echo $pages;
		} else {
			if ( $_SESSION['type'] == 1 ) {
				$SQLOG = "INSERT INTO ebook_log (log,book_id,teacher_id,edit_date) VALUES ";
				$SQLOG .= "('delete bookmark successfully.','".$book_id."','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."')";
			} else {
				$SQLOG = "INSERT INTO student_log (log,book_id,student_id,edit_date) VALUES ";
				$SQLOG .= "('delete bookmark successfully.','".$book_id."','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."')";				
			}
			$objConn->query($SQLOG);
			
			echo "[]"; // empty json
		}
		
		closeConnection();

	} else {
		echo "[]"; // empty json
	}
} else {
	echo "[]"; // empty json
}


?>