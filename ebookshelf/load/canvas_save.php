<?php

// error_reporting(E_ALL);
// ini_set('display_errors', 1);

session_start();
$response = new stdClass;
$response->success = false;

if(isset($_SESSION['user_session'])) {

	if( isset($_POST['type']) && $_POST['type']=='1' ) {
		// add new
		if(isset($_POST['book_id']) && isset($_POST['page']) && isset($_POST['className']) && isset($_POST['data']) && isset($_POST['data2'])  ) {

			require_once '../inc/settings.inc.php';
			require_once '../inc/functions.inc.php';

			$book_id = trim($_POST['book_id']);
			$page = trim($_POST['page']);
			$class = trim($_POST['className']);
			$data = $_POST['data'];
			$data2 = $_POST['data2'];

			openConnection();

			if ( $_SESSION['type'] == 1 ) {
				$SQL = "INSERT INTO ebook_custom (data,data2,teacher_id,book_id,page,class,x,y,width,height,disabled,edit_user,edit_date) ";
				$SQL .= "VALUES ('$data','$data2','".$_SESSION['user_session']."','$book_id','$page','$class','0','0','100%','100%','0','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."')";
			} else {
				$SQL = "INSERT INTO student_custom (data,data2,student_id,book_id,page,class,x,y,width,height,disabled,edit_user,edit_date) ";
				$SQL .= "VALUES ('$data','$data2','".$_SESSION['user_session']."','$book_id','$page','$class','0','0','100%','100%','0','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."')";
			}

			if ( $objConn->query($SQL) ) {
				$response->success = true;
				$response->new_id = $objConn->insert_id;
				$response->page = $page;

				if ( $_SESSION['type'] == 1 ) {
					$SQLOG = "INSERT INTO ebook_log (log,page,class,book_id,teacher_id,edit_date) VALUES ";
					$SQLOG .= "('create custom draw successfully. (".$objConn->insert_id.")','".$page."','".$class."','".$book_id."','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."')";
				} else {
					$SQLOG = "INSERT INTO student_log (log,page,class,book_id,student_id,edit_date) VALUES ";
					$SQLOG .= "('create custom draw successfully. (".$objConn->insert_id.")','".$page."','".$class."','".$book_id."','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."')";					
				}
				$objConn->query($SQLOG);

			}

			closeConnection();

		}


	} elseif( isset($_POST['type']) && $_POST['type']=='2' ) {

		if(isset($_POST['custom_id']) && isset($_POST['data']) && isset($_POST['data2'])  ) {

			require_once '../inc/settings.inc.php';
			require_once '../inc/functions.inc.php';

			$custom_id = trim($_POST['custom_id']);
			$data = $_POST['data'];
			$data2 = $_POST['data2'];

			openConnection();

			if ( $_SESSION['type'] == 1 ) {
				$SQL = "UPDATE ebook_custom SET ";
				$SQL .= "data='".$data."',";
				$SQL .= "data2='".$data2."',";
				$SQL .= "edit_user='".$_SESSION['user_session']."',";
				$SQL .= "edit_date='".date("Y-m-d H:i:s")."' ";
				$SQL .= "WHERE custom_id=$custom_id";
				
				if ( $objConn->query($SQL) ) {

					$SQL2 = "SELECT * FROM ebook_custom WHERE custom_id=$custom_id";
					if ($result2 = $objConn->query($SQL2)) {
						if ( $result2->num_rows > 0 ) {
							$row2 = $result2->fetch_assoc();
							$page = $row2['page'];
							$class = $row2['class'];
							$book_id = $row2['book_id'];

							$response->success = true;
							$response->cid = $custom_id;
							$response->page = $page;

							$SQLOG = "INSERT INTO ebook_log (log,page,class,book_id,teacher_id,edit_date) VALUES ";
							$SQLOG .= "('update custom draw successfully. (".$custom_id.")','".$page."','".$class."','".$book_id."','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."')";
							$objConn->query($SQLOG);
						}
					}

				}

			} else {
				
				$SQL = "UPDATE student_custom SET ";
				$SQL .= "data='".$data."',";
				$SQL .= "data2='".$data2."',";
				$SQL .= "edit_user='".$_SESSION['user_session']."',";
				$SQL .= "edit_date='".date("Y-m-d H:i:s")."' ";
				$SQL .= "WHERE custom_id=$custom_id";				
				
				if ( $objConn->query($SQL) ) {

					$SQL2 = "SELECT * FROM student_custom WHERE custom_id=$custom_id";
					if ($result2 = $objConn->query($SQL2)) {
						if ( $result2->num_rows > 0 ) {
							$row2 = $result2->fetch_assoc();
							$page = $row2['page'];
							$class = $row2['class'];
							$book_id = $row2['book_id'];

							$response->success = true;
							$response->cid = $custom_id;
							$response->page = $page;

							$SQLOG = "INSERT INTO student_log (log,page,class,book_id,student_id,edit_date) VALUES ";
							$SQLOG .= "('update custom draw successfully. (".$custom_id.")','".$page."','".$class."','".$book_id."','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."')";
							$objConn->query($SQLOG);
						}
					}

				}

			}


			closeConnection();

		}

	} elseif( isset($_POST['type']) && $_POST['type']=='3' ) {

		if( isset($_POST['custom_id']) ) {

			require_once '../inc/settings.inc.php';
			require_once '../inc/functions.inc.php';

			openConnection();

			$custom_id = trim($_POST['custom_id']);

			if ( $_SESSION['type'] == 1 ) {
				$SQL = "UPDATE ebook_custom SET ";
				$SQL .= "disabled=1,";
				$SQL .= "edit_user='".$_SESSION['user_session']."',";
				$SQL .= "edit_date='".date("Y-m-d H:i:s")."' ";
				$SQL .= "WHERE custom_id=$custom_id";
				
				if ( $objConn->query($SQL) ) {

					$SQL2 = "SELECT * FROM ebook_custom WHERE custom_id=$custom_id";
					if ($result2 = $objConn->query($SQL2)) {
						if ( $result2->num_rows > 0 ) {
							$row2 = $result2->fetch_assoc();
							$page = $row2['page'];
							$class = $row2['class'];
							$book_id = $row2['book_id'];

							$response->success = true;
							$response->cid = 0;
							$response->page = $page;

							$SQLOG = "INSERT INTO ebook_log (log,page,class,book_id,teacher_id,edit_date) VALUES ";
							$SQLOG .= "('delete custom draw successfully. (".$custom_id.")','".$page."','".$class."','".$book_id."','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."')";
							$objConn->query($SQLOG);
						}
					}

				}

			} else {
				$SQL = "UPDATE student_custom SET ";
				$SQL .= "disabled=1,";
				$SQL .= "edit_user='".$_SESSION['user_session']."',";
				$SQL .= "edit_date='".date("Y-m-d H:i:s")."' ";
				$SQL .= "WHERE custom_id=$custom_id";
				
				if ( $objConn->query($SQL) ) {

					$SQL2 = "SELECT * FROM student_custom WHERE custom_id=$custom_id";
					if ($result2 = $objConn->query($SQL2)) {
						if ( $result2->num_rows > 0 ) {
							$row2 = $result2->fetch_assoc();
							$page = $row2['page'];
							$class = $row2['class'];
							$book_id = $row2['book_id'];

							$response->success = true;
							$response->cid = 0;
							$response->page = $page;

							$SQLOG = "INSERT INTO student_log (log,page,class,book_id,student_id,edit_date) VALUES ";
							$SQLOG .= "('delete custom draw successfully. (".$custom_id.")','".$page."','".$class."','".$book_id."','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."')";
							$objConn->query($SQLOG);
						}
					}

				}

			}


			closeConnection();

		}


	}

}

echo json_encode($response);


?>