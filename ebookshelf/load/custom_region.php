<?php

 // error_reporting(E_ALL);
 // ini_set('display_errors', 1);

session_start();

if(isset($_SESSION['user_session'])) {
	
	if(isset($_POST['book_id']) && isset($_POST['page']) ) {
	 
		require_once '../inc/settings.inc.php';
		require_once '../inc/functions.inc.php';
		
		$book_id = trim($_POST['book_id']);
		$page = trim($_POST['page']);

		openConnection();

		if ( $_SESSION['type'] == 1 ) {
			$SQL = "SELECT * FROM ebook_custom WHERE teacher_id='".$_SESSION['user_session']."' AND book_id=$book_id AND page=$page AND disabled=0 ORDER BY class ASC";
		} else {
			$SQL = "SELECT * FROM student_custom WHERE student_id='".$_SESSION['user_session']."' AND book_id=$book_id AND page=$page AND disabled=0 ORDER BY class ASC";
		}

		if ($result = $objConn->query($SQL)) {
			$row_cnt = $result->num_rows;
			if ( $row_cnt > 0 ) {
				echo "[";
				$i=0;
				while($row = $result->fetch_assoc()) {
					if ( $i > 0 ) echo ',';
echo '{
	"x":"'.$row['x'].'",
	"y":"'.$row['y'].'",
	"width":"'.$row['width'].'",
	"height":"'.$row['height'].'",
	"class":"'.$row['class'].'",
	"custom_id":"'.$row['custom_id'].'",
	"data":
		{';
if ( $row['data2'] <> '' ) { echo '"object":'.$row['data2'].','; }
echo '"content": '.json_encode($row['data']).'
	}
}';
					$i++;
				}
				echo "]";

				// $row = $result->fetch_assoc();
				
				if ( $_SESSION['type'] == 1 ) {
					$SQLOG = "INSERT INTO ebook_log (log,page,book_id,teacher_id,edit_date) VALUES ";
					$SQLOG .= "('get custom region successfully.','".$page."','".$book_id."','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."')";
				} else {
					$SQLOG = "INSERT INTO student_log (log,page,book_id,student_id,edit_date) VALUES ";
					$SQLOG .= "('get custom region successfully.','".$page."','".$book_id."','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."')";					
				}
				// $objConn->query($SQLOG);

				// echo $row['pages'];
			} else {
				echo "[]";
			}
			$result->close();
		} else {
			echo "[]"; // empty json
		}

		closeConnection();

	} else {
		echo "[]"; // empty json
	}
} else {
	echo "[]"; // empty json
}


?>