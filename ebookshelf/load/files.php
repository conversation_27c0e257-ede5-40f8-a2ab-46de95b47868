<?php

// error_reporting(E_ALL);
// ini_set('display_errors', 1);

session_start();

if(isset($_SESSION['user_session'])) {
	
	if( isset($_GET['id']) ) {
	 
		require_once '../inc/settings.inc.php';
		require_once '../inc/functions.inc.php';
		
		openConnection();
		
		$file_id = trim($_GET['id']);

		$SQL2 = "SELECT * FROM ebook_file WHERE file_id=$file_id AND teacher_id=".$_SESSION['user_session']." ";
		if ($result2 = $objConn->query($SQL2)) {
			if ( $result2->num_rows > 0 ) {
				$row2 = $result2->fetch_assoc();
				$file = $row2['file_path'];
				$file_name = $row2['file_name'];
				$file_type = $row2['file_type'];
				$disabled = $row2['disabled'];
				
				if ( $disabled == 0 ) {
					
					if (file_exists($file)) {
						header('Content-type: '.$file_type);
						header('Content-Disposition: inline; filename="'.$file_name.'"');
						readfile($file);
							
						$SQLOG = "INSERT INTO ebook_log (log,class,teacher_id,edit_date) VALUES ";
						$SQLOG .= "('file download successfully. (".$file_id.")','file','".$_SESSION['user_session']."','".date("Y-m-d H:i:s")."')";
						$objConn->query($SQLOG);

					} else {
						echo "<h1>Content error</h1><p>The file does not exist!</p>";
					}
					
				} else {
					echo "<h1>Content error</h1><p>The file was deleted!</p>";
				}
			} else {
				echo "<h1>Content error</h1><p>The file does not exist!</p>";
			}
		} else {
			echo "<h1>Content error</h1><p>The file does not exist!</p>";
		}
		
		closeConnection();

	}
	exit;
}

echo "<h1>Content error</h1><p>The file does not exist!</p>";

?>