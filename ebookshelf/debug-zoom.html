<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zoom Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .test-button:hover {
            background: #005a87;
        }
        .console-output {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 300px;
            overflow-y: auto;
            margin: 20px 0;
            white-space: pre-wrap;
        }
        .status {
            margin: 20px 0;
            padding: 10px;
            background: #e8f4f8;
            border-left: 4px solid #007cba;
            border-radius: 4px;
        }
        .error {
            background: #ffe8e8;
            border-left-color: #ff0000;
        }
        .success {
            background: #e8ffe8;
            border-left-color: #00aa00;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔍 Zoom Functionality Debug Test</h1>
        
        <div class="status">
            <strong>Debug Instructions:</strong>
            <ol>
                <li>Open the browser's Developer Tools (F12)</li>
                <li>Go to the Console tab</li>
                <li>Open any book page (e.g., /ebookshelf/book/cs01-chi/)</li>
                <li>Click the zoom buttons and watch the console output</li>
                <li>Check for any JavaScript errors or warnings</li>
            </ol>
        </div>

        <h3>🎯 What to Look For:</h3>
        <ul>
            <li><strong>Button Click Detection:</strong> "Zoom button clicked: zoom-icon-in" or "zoom-icon-out"</li>
            <li><strong>Current Zoom Level:</strong> "Current zoom before zoom-in: 1"</li>
            <li><strong>Target Zoom Level:</strong> "Target zoom level: 1.5"</li>
            <li><strong>zoomTo Method Call:</strong> "zoomTo called with targetZoom: 1.5"</li>
            <li><strong>Zoom Data Check:</strong> "Zoom data not initialized" (error) or zoom calculations</li>
            <li><strong>Fallback Activation:</strong> "zoomTo failed, falling back to original method"</li>
        </ul>

        <h3>🚨 Common Issues to Check:</h3>
        <div class="status error">
            <strong>Potential Problems:</strong>
            <ul>
                <li><strong>jQuery not loaded:</strong> "$ is not defined"</li>
                <li><strong>Zoom plugin not loaded:</strong> "zoom is not a function"</li>
                <li><strong>Magazine viewport not found:</strong> Check if $('.magazine-viewport') exists</li>
                <li><strong>Event binding issues:</strong> Click events not properly attached</li>
                <li><strong>CSS conflicts:</strong> Buttons not clickable due to z-index or positioning</li>
            </ul>
        </div>

        <h3>🔧 Manual Test Commands:</h3>
        <p>You can also test the zoom functionality manually in the browser console:</p>
        <div class="console-output">
// Check if jQuery is loaded
console.log('jQuery version:', $.fn.jquery);

// Check if zoom plugin is available
console.log('Zoom plugin available:', typeof $('.magazine-viewport').zoom);

// Check if magazine viewport exists
console.log('Magazine viewport found:', $('.magazine-viewport').length);

// Check current zoom level
console.log('Current zoom level:', $('.magazine-viewport').zoom('value'));

// Test zoom buttons exist
console.log('Zoom-in buttons found:', $('.zoom-icon-in').length);
console.log('Zoom-out buttons found:', $('.zoom-icon-out').length);

// Test manual zoom
$('.magazine-viewport').zoom('zoomTo', 1.5);

// Test original zoom methods
$('.magazine-viewport').zoom('zoomIn');
$('.magazine-viewport').zoom('zoomOut');
        </div>

        <h3>✅ Expected Console Output (Working):</h3>
        <div class="console-output">
Zoom button clicked: zoom-icon zoom-icon-in
Zoom-in button clicked
Current zoom before zoom-in: 1
Target zoom level: 1.5
zoomTo called with targetZoom: 1.5 event: undefined
Current zoom level: 1
Calculated duration: 300 for zoom diff: 0.5
Zooming in to level: 1.5
        </div>

        <h3>❌ Error Console Output (Not Working):</h3>
        <div class="console-output">
Uncaught TypeError: Cannot read property 'zoom' of undefined
    at HTMLDivElement.&lt;anonymous&gt; (loadapp.js:1228)
    at HTMLDivElement.dispatch (jquery.min.1.7.js:3)
    at HTMLDivElement.v.handle (jquery.min.1.7.js:3)

OR

Uncaught Error: zoomTo is not a method
    at HTMLDivElement.&lt;anonymous&gt; (loadapp.js:1247)
        </div>

        <h3>🛠️ Quick Fixes to Try:</h3>
        <div class="status">
            <ol>
                <li><strong>Refresh the page</strong> to ensure all scripts are loaded</li>
                <li><strong>Check network tab</strong> for failed script loads</li>
                <li><strong>Verify file paths</strong> in the HTML head section</li>
                <li><strong>Test with original zoom methods</strong> if new ones fail</li>
                <li><strong>Clear browser cache</strong> to ensure latest files are loaded</li>
                <li><strong>Check CSS z-index conflicts</strong> - zoom icons should be clickable</li>
                <li><strong>Verify jQuery and zoom plugin loading order</strong></li>
            </ol>
        </div>

        <h3>🔍 Step-by-Step Debugging Process:</h3>
        <div class="status">
            <strong>Follow these steps in order:</strong>
            <ol>
                <li><strong>Open Developer Tools (F12)</strong> and go to Console tab</li>
                <li><strong>Navigate to a book page</strong> (e.g., /ebookshelf/book/cs01-chi/)</li>
                <li><strong>Look for setup messages:</strong>
                    <ul>
                        <li>"Setting up zoom icon event handlers..."</li>
                        <li>"Zoom icons found: 2" (should be 2)</li>
                        <li>"Zoom-in icons found: 1" and "Zoom-out icons found: 1"</li>
                    </ul>
                </li>
                <li><strong>Click a zoom button</strong> and check for:
                    <ul>
                        <li>"Zoom button clicked: zoom-icon zoom-icon-in"</li>
                        <li>"Zoom-in button clicked"</li>
                        <li>"Current zoom before zoom-in: 1"</li>
                    </ul>
                </li>
                <li><strong>If no click messages appear:</strong> The event handlers aren't bound properly</li>
                <li><strong>If click messages appear but zoom fails:</strong> The zoom plugin has issues</li>
            </ol>
        </div>

        <h3>🚨 Common Issues and Solutions:</h3>
        <div class="status error">
            <strong>Issue 1: No click events detected</strong>
            <ul>
                <li><strong>Cause:</strong> Event handlers not bound or CSS blocking clicks</li>
                <li><strong>Solution:</strong> Check if loadapp.js is loaded and executed</li>
                <li><strong>Test:</strong> Run <code>$('.zoom-icon').length</code> in console</li>
            </ul>
        </div>

        <div class="status error">
            <strong>Issue 2: "zoomTo is not a method" error</strong>
            <ul>
                <li><strong>Cause:</strong> New zoomTo method not properly added to zoom plugin</li>
                <li><strong>Solution:</strong> Fallback to original zoom methods will activate</li>
                <li><strong>Test:</strong> Check if original zoom works: <code>$('.magazine-viewport').zoom('zoomIn')</code></li>
            </ul>
        </div>

        <div class="status error">
            <strong>Issue 3: "Magazine viewport not found" error</strong>
            <ul>
                <li><strong>Cause:</strong> Page structure issue or timing problem</li>
                <li><strong>Solution:</strong> Ensure magazine is fully loaded before zoom setup</li>
                <li><strong>Test:</strong> Run <code>$('.magazine-viewport').length</code> in console</li>
            </ul>
        </div>

        <div class="status success">
            <strong>Next Steps:</strong> Once you identify the issue from the console output, 
            we can implement the appropriate fix. The debugging information will help pinpoint 
            exactly where the zoom process is failing.
        </div>
    </div>

    <script>
        // Add some basic debugging for this page
        console.log('Debug page loaded');
        console.log('jQuery available:', typeof $ !== 'undefined');
        
        if (typeof $ !== 'undefined') {
            console.log('jQuery version:', $.fn.jquery);
        }
    </script>
</body>
</html>
