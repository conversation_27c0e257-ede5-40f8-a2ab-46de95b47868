<?php
require_once 'settings.inc.php';
require_once 'functions.inc.php';

$actual_link = "http://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
$user_login = isset($_SESSION['user_name']) ? $_SESSION['user_name'] : 'unknown';

openConnection();

$SQL = "SELECT teacher_name FROM ebook_teacher WHERE teacher_id='".$_SESSION['user_session']."'";
if ($result = $objConn->query($SQL)) {
	$row_cnt = $result->num_rows;
	if ( $row_cnt == 1 ) {
		$row = $result->fetch_assoc();
		$user_name = $row['teacher_name'];
		$user_login = $user_name; // 使用从数据库获取的用户名
		
		$SQLOG = "INSERT INTO ebook_log (log,path,page,class,teacher_id,teacher_login,edit_date) VALUES ";
		$SQLOG .= "('user check successfully.','".$actual_link."',0,'user_check','".$_SESSION['user_session']."','$user_login','".date("Y-m-d H:i:s")."')";
		// $objConn->query($SQLOG);

	} else {
		
		$SQLOG = "INSERT INTO ebook_log (log,path,page,class,teacher_id,teacher_login,edit_date) VALUES ";
		$SQLOG .= "('user check error: 1','".$actual_link."',0,'user_check','".$_SESSION['user_session']."','$user_login','".date("Y-m-d H:i:s")."')";
		$objConn->query($SQLOG);

		session_unset();
		if(session_destroy()) {
			header("Location: /ebookshelf/login.php");
		}
	}
	$result->close();
} else {
	
	$SQLOG = "INSERT INTO ebook_log (log,path,page,class,teacher_id,teacher_login,edit_date) VALUES ";
	$SQLOG .= "('user check error: 2','".$actual_link."',0,'user_check','".$_SESSION['user_session']."','$user_login','".date("Y-m-d H:i:s")."')";
	$objConn->query($SQLOG);

	session_unset();
	if(session_destroy()) {
		header("Location: /ebookshelf/login.php");
	}
}
closeConnection();


?>
