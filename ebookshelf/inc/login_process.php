<?php

// error_reporting(E_ALL);
// ini_set('display_errors', 1);

session_start();
require_once 'settings.inc.php';
require_once 'functions.inc.php';

$actual_link = "http://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";

if( isset($_POST['login']) && isset($_POST['password']) ) {

	$user_login = trim($_POST['login']);
	$user_password = trim($_POST['password']);
	$loginok = false;

	openConnection();

	// check student

	$SQL = "SELECT student_id, pwd, student_name, class_id, teacher_id FROM student WHERE deleted=0 AND wrong_count<10 AND student_id='$user_login' AND pwd='$user_password'";

	if ($result = $objConn->query($SQL)) {
		
		$row_cnt = $result->num_rows;
		if ( $row_cnt == 1 ) {
			$row = $result->fetch_assoc();
			$result->close();
			$_SESSION['user_session'] = $row['student_id'];
			$_SESSION['type'] = 2;
			$_SESSION['user_name'] = $row['student_name'];
			$_SESSION['class_id'] = $row['class_id'];
			$_SESSION['teacher_id'] = $row['teacher_id'];

			$SQL2 = "UPDATE student SET wrong_count=0, sid='".session_id()."', last_login='".date("Y-m-d H:i:s")."' WHERE student_id='$user_login'";
			$objConn->query($SQL2);

			$SQLOG = "INSERT INTO student_log (log,path,student_id,student_name,edit_date) VALUES ";
			$SQLOG .= "('log in successfully.','".$actual_link."','".$_SESSION['user_session']."','".$_SESSION['user_name']."','".date("Y-m-d H:i:s")."')";
			$objConn->query($SQLOG);

			$SQL = "SELECT books FROM student_class WHERE class_id='".$_SESSION['class_id']."' AND deleted=0 AND teacher_id='".$_SESSION['teacher_id']."'";
			if ($result = $objConn->query($SQL)) {
				$i=0;
				$booklist = array();
				$row = $result->fetch_assoc();
				$books = explode(',', $row['books']);
				$result->close();

				foreach ( $books as $bookid ) {
					$SQL = "SELECT * FROM ebook_book WHERE book_id='$bookid'";
					if ($result = $objConn->query($SQL)) {
						$row = $result->fetch_assoc();
						array_push($booklist, array( $row['book_id'] , $row['book_title'] , 'books/'.$row['book_path'], $row['book_pic'] ) );
						$result->close();
					}
				}

				$_SESSION['booklist'] = $booklist;

			}
			
			echo "ok"; // log in
			$loginok = true;
		}
	}


	if ( $loginok == false ) {

		$SQL = "SELECT t.teacher_id, t.teacher_pwd, t.books, s.schCode FROM ebook_teacher t, school s WHERE t.school_id=s.schId AND t.expire_date>=CURDATE() AND t.disabled=0 AND t.wrong_count<10 AND t.teacher_login='$user_login' AND t.teacher_pwd='$user_password'";

		if ($result = $objConn->query($SQL)) {
			$row_cnt = $result->num_rows;
			if ( $row_cnt == 1 ) {
				$row = $result->fetch_assoc();
				$booklist = array();

				$_SESSION['user_session'] = $row['teacher_id'];
				$_SESSION['type'] = 1;
				$_SESSION['school'] = $row['schCode'];
				
				$books = explode(',', $row['books']);
				foreach ( $books as $bookid ) {
					$SQL = "SELECT * FROM ebook_book WHERE book_id='$bookid'";
					if ($result2 = $objConn->query($SQL)) {
						$row2 = $result2->fetch_assoc();
						array_push($booklist, array( $row2['book_id'] , $row2['book_title'] , 'book/'.$row2['book_path'], $row2['book_pic'] ) );
						$result2->close();
					}
				}

				$_SESSION['booklist'] = $booklist;


				$SQL2 = "UPDATE ebook_teacher SET wrong_count=0, sid='".session_id()."', last_login='".date("Y-m-d H:i:s")."' WHERE teacher_login='$user_login'";
				$objConn->query($SQL2);

				$SQLOG = "INSERT INTO ebook_log (log,path,page,class,teacher_id,teacher_login,edit_date) VALUES ";
				$SQLOG .= "('log in successfully.','".$actual_link."',0,'login','".$_SESSION['user_session']."','$user_login','".date("Y-m-d H:i:s")."')";
				$objConn->query($SQLOG);

				if ( $row['teacher_id'] == "1" ) {
					echo "good"; // admin
				} else {
					echo "ok"; // log in
				}
				

			} else {

				$SQL2 = "UPDATE ebook_teacher SET wrong_count=wrong_count+1, sid='".session_id()."', last_login='".date("Y-m-d H:i:s")."' WHERE teacher_login='$user_login'";
				$objConn->query($SQL2);

				session_unset();
				session_destroy();
				$SQLOG = "INSERT INTO ebook_log (log,path,teacher_id,teacher_login,edit_date) VALUES ";
				$SQLOG .= "('log in error: 1','".$actual_link."','".$_SESSION['user_session']."','$user_login','".date("Y-m-d H:i:s")."')";
				$objConn->query($SQLOG);
				echo "登入名稱或密碼錯誤。"; // wrong details

			}
			$result->close();
		} else {

			session_unset();
			session_destroy();
			$SQLOG = "INSERT INTO ebook_log (log,path,teacher_id,teacher_login,edit_date) VALUES ";
			$SQLOG .= "('log in error: 2','".$actual_link."','".$_SESSION['user_session']."','$user_login','".date("Y-m-d H:i:s")."')";
			$objConn->query($SQLOG);
			echo "登入名稱或密碼錯誤。"; // wrong details
		}

	}

	closeConnection();

}
?>