﻿/*
 * loadapp - ebookshelf 2022-06-10
*/

function loadApp() {

	var resized = 0;
	var bookmark = {};
	var lc;
	var lcPen;
	var lcHighlighter;
	var lcEraser;
	var testClass = [];
	var testClass2 = [];
	var selectClass = '0';
	
	// 全局缩放同步函数
	function syncLiterallyCanvasZoom() {
		if (lc && typeof lc.setZoom === 'function') {
			var currentZoomValue = $('.magazine-viewport').zoom("value");
			lc.setZoom(currentZoomValue);
		}
	}

	if ( bookLang == 'eng' ) {
		var displayText = {
			'01':'File cannot be larger than 5 MB.',
			'02':'File is not MP3 or PDF.',
			'03':'System error.',
			'04':'Upload finished.',
			'05':'Network error.',
			'06':'Delete finished.',
			'07':'Add new finished.',
			'08':'Edit',
			'09':'Save',
			'10':'System error: cannot save data.',
			'11':'Error Message',
			'12':'Network error: cannot connect to server.',
			'13':'File upload',
			'14':'Delete',
			'15':'Insert'
		};
	} else {
		var displayText = {
			'01':'檔案太大，檔案不得超過 5 MB。',
			'02':'檔案類型不是 pdf 或者 mp3。',
			'03':'系統錯誤。',
			'04':'上傳完成。',
			'05':'網路錯誤。',
			'06':'刪除完成。',
			'07':'加入成功。',
			'08':'修改',
			'09':'儲存',
			'10':'系統錯誤：無法儲存數據。',
			'11':'錯誤訊息',
			'12':'網路錯誤：無法連接到伺服器。',
			'13':'上傳檔案',
			'14':'刪除',
			'15':'插入'
		};		
	}

	document.addEventListener('gesturestart', function (e) {
		e.preventDefault();
	});
	$( "#lc" ).bind('touchend', function(e) {
		if ( !$(e.target).is("a") ) {
			e.preventDefault();
		}
	});
	$( ".home-icon" ).bind('touchend', function(e) {
		e.preventDefault();
	});
	$( ".zoom-icon" ).bind('touchend', function(e) {
		e.preventDefault();
	});
	$( ".page-icon" ).bind('touchend', function(e) {
		e.preventDefault();
	});
	$( ".content-icon" ).bind('touchend', function(e) {
		e.preventDefault();
	});
	$( ".note-icon" ).bind('touchend', function(e) {
		e.preventDefault();
	});
	$( ".highlighter-icon" ).bind('touchend', function(e) {
		e.preventDefault();
	});
	$( ".bookmark-icon" ).bind('touchend', function(e) {
		e.preventDefault();
	});
	$( ".test-icon" ).bind('touchend', function(e) {
		e.preventDefault();
	});
	$( "#testExcel" ).bind('touchend', function(e) {
		e.preventDefault();
	});
	$( "#testExcel2" ).bind('touchend', function(e) {
		e.preventDefault();
	});


	getBookmark();

	function getBookmark() {
		$.ajax({
			dataType: "json",
			url: '../../load/bookmark.php',
			data: {book_id:bookID},
			type: "POST",
			success: function(data){
				$('#bookmark-content').empty();
				bookmark = {};
				$.each(data, function(key, region) {
					// addRegion(region, element);
					bookmark[region.page] = region.name;
					var html = '<div class="bookmark-to-page" data-page="' + region.page + '">' + region.name + '</div>';
					$('#bookmark-content').append(html);
				});
			}
		});
	}

	function saveBookmark() {
		var pages = '[{';
		var i = 0;
		$.each(bookmark, function( index, value ) {
			// console.log( index + ": " + value );
			if ( i > 0) {
				pages += '},{';
			}
			pages += '"page":"' + index + '","name":"' + value + '"';
			i++;
		});
		pages += '}]';
		if ( pages == '[{}]' ) pages = '';
		$.ajax({
			dataType: "json",
			url: '../../load/bookmark_add.php',
			data: {
				book_id:bookID,
				pages:pages
			},
			type: "POST",
			success: function(data){
				$('#bookmark-content').empty();
				bookmark = {};
				$.each(data, function(key, region) {
					// addRegion(region, element);
					bookmark[region.page] = region.name;
					var html = '<div class="bookmark-to-page" data-page="' + region.page + '">' + region.name + '</div>';
					$('#bookmark-content').append(html);
				});
			}
		});
	}

	$( "#dialogBookmark" ).dialog({
 		autoOpen: false,
		width: 200,
		height: 300,
		resizable: false,
		modal: true,
		draggable: false,
		open: function( event, ui ) {
			$( this ).dialog( "option", "position", [64,334] );
		},
		create: function(event,ui) {
			$( "#dialogBookmark" ).bind('touchend', function(e) {
				e.preventDefault();
			});
		}
	});

	$( "#dialogNotes" ).dialog({
 		autoOpen: false,
		width: 350,
		height: 'auto',
		resizable: false,
		modal: false,
		position: { my: "center", at: "center", of: window },
		open: function( event, ui ) {
			$( this ).dialog( "option", "position", { my: "center", at: "center", of: window } );
		},
		beforeClose: function( event, ui ) {
			$( this ).dialog( "option", "modal", false );
		},
		create: function(event,ui) {
			$( "#dialogNotes" ).bind('touchend', function(e) {
				e.preventDefault();
			});
		}
	});


	function loadFiles() {
		$.ajax({
			dataType: "json",
			url: '../../load/file_list.php',
			type: "POST",
			success: function(data){
				$('#dialogFile table').find('tr:gt(0)').remove();
				// $('#dialogFile').find('.error').text('');
				$.each(data.files, function(key, file) {
					// addRegion(region, element);
					var html = '<tr style="height:40px;"><td style="text-align:center;">'+file.file_id+'</td><td><a href="../../load/files.php?id='+file.file_id+'" target="_blank">';
					html += file.file_name;
					html += '</a></td><td style="text-align:center;">';
					html += file.edit_date;
					html += '</td><td style="text-align:center;"><button class="file-insert-btn" data-id="'+file.file_id+'">'+displayText['15']+'</button></td>';
					html += '<td style="text-align:center;"><button class="file-delete-btn" data-id="'+file.file_id+'">'+displayText['14']+'</button></td></tr>';
					$('#dialogFile table').append(html);
				});
			}
		});
	}

	$( "#dialogFile" ).dialog({
 		autoOpen: false,
		width: '500',
		height: '500',
		resizable: false,
		modal: true,
		position: { my: "center", at: "center", of: window },
		buttons: [],
		open: function( event, ui ) {
			$( this ).dialog( "option", "position", { my: "center", at: "center", of: window } );
			// load
			loadFiles();
		},
		beforeClose: function( event, ui ) {
			$('#dialogFile').find('.error').text('');
		},
		create: function(event,ui) {

			$( "#dialogFile" ).bind('touchend', function(e) {
				if ( !$(e.target).is("input") && !$(e.target).is("a") && !$(e.target).is("button") ) {
					e.preventDefault();
				}
			});

			$( "#dialogFile" ).delegate( '#file-upload', 'click', function() {
				if ( $('#file-content').val() != '' ) {
					$('#dialogFile').find('.error').text('');
					var file = $('#file-content')[0].files[0];

					if(file.name.length < 1) {
						// do nothing
					} else if(file.size > 5000000) {
						$('#dialogFile').find('.error').text( displayText['01'] );
					}
					else if(file.type != 'application/pdf' && file.type != 'audio/mpeg' && file.type != 'audio/mp3' && file.type != 'image/png' && file.type != 'image/jpg'  ) {
						$('#dialogFile').find('.error').text( displayText['02'] );
						// $('#dialogFile').find('.error').text(file.type);
					} else {
						// ajax upload
						var formData = new FormData();
						formData.append('file', file );

						$.ajax({
							url : '../../load/file_upload.php',
							type : 'POST',
							data : formData,
							dataType: "json",
							processData: false,  // tell jQuery not to process the data
							contentType: false,  // tell jQuery not to set contentType
							success : function(data) {
							   if ( !data.success ) {
								   $('#dialogFile').find('.error').text( displayText['03'] );
								   console.log(data.message);
							   } else {
								   $('#file-content').val('');
								   $('#dialogFile').find('.error').text( displayText['04'] );
								   loadFiles();
							   }
							},
							error: function (xhr, ajaxOptions, thrownError) {
								$('#dialogFile').find('.error').text( displayText['05'] );
								console.log(xhr);
								console.log(thrownError);
							}

						});
					}
				}
			});

			$( "#dialogFile" ).delegate( '.file-delete-btn', 'click', function() {
				var fileID = $(this).attr('data-id');

				$.ajax({
					url : '../../load/file_del.php',
					type : 'POST',
					data : {
						file_id: fileID
					},
					dataType: "json",
					success : function(data) {
					   if ( !data.success ) {
						   $('#dialogFile').find('.error').text( displayText['03'] );
						   console.log(data.message);
					   } else {
						   $('#dialogFile').find('.error').text( displayText['06'] );
						   loadFiles();
						   // reload all custom regions
					   }
					},
					error: function (xhr, ajaxOptions, thrownError) {
						$('#dialogFile').find('.error').text( displayText['05'] );
						console.log(xhr);
						console.log(thrownError);
					}

				});

			});

			$( "#dialogFile" ).delegate( '.file-insert-btn', 'click', function() {
				var fileID = $(this).attr('data-id');

				var cid = $( '#dialogCustomNote' ).find('#custom-id').val();
				var data = $( '#dialogCustomNote' ).find('textarea').val() + '[#file' + fileID + ']';

				$.ajax({
					dataType: "json",
					url: '../../load/custom_region_edit.php',
					data: {
						cid: cid,
						data: data
					},
					type: "POST",
					success: function(data){
						if (data.success) {
							$('#dialogFile').find('.error').text( displayText['07'] );
							$( "#dialogCustomNote" ).dialog( "close");
							var element = $('div.p' + data.page);
							// var element = document.getElementsByClassName("p" + data.page);
							loadCustomRegions( data.page, element, data.cid);
						} else {
						   $('#dialogFile').find('.error').text( displayText['03'] );
						   console.log(data.message);
						}
					},
					error: function (xhr, ajaxOptions, thrownError) {
						$('#dialogFile').find('.error').text( displayText['05'] );
						console.log(xhr);
						console.log(thrownError);
					}
				});

			});
		}
	});

	$( "#dialogCustomNote" ).dialog({
 		autoOpen: false,
		width: 'auto',
		height: 'auto',
		minHeight: 300,
		minWidth: 300,
		resizable: false,
		modal: false,
		position: { my: "center", at: "center", of: window },
		buttons: [
			{
				text: displayText['08'],
				click: function(event){

					if ( $(event.delegateTarget).text() == displayText['08']) {
						$( this ).find('p').hide();
						$( this ).find('textarea').show();
						$(event.delegateTarget).find('span').text( displayText['09'] );
					} else {
						var cid = $( this ).find('#custom-id').val();
						var data = $( this ).find('textarea').val();
						$.ajax({
							dataType: "json",
							url: '../../load/custom_region_edit.php',
							data: {
								cid: cid,
								data: data
							},
							type: "POST",
							success: function(data){
								if (data.success) {
									$( "#dialogCustomNote" ).dialog( "close");
									var element = $('div.p' + data.page);
									loadCustomRegions( data.page, element, data.cid);
								} else {
									$( "#dialogNotes" ).find('p').html( displayText['03'] );
									$( "#dialogNotes" ).dialog('option', 'title', displayText['11']);
									$( "#dialogNotes" ).dialog( "option", "modal", true );
									$( "#dialogNotes" ).dialog('open');
								}
							},
							error: function (xhr, ajaxOptions, thrownError) {
								$( "#dialogNotes" ).find('p').html( displayText['05'] );
								$( "#dialogNotes" ).dialog('option', 'title', displayText['11']);
								$( "#dialogNotes" ).dialog( "option", "modal", true );
								$( "#dialogNotes" ).dialog('open');
							}
						});


					}

				}
			},
			{
				text: displayText['13'],
				click: function(event){
					$( "#dialogFile" ).dialog('open');
				}
			},
			{
				text: displayText['14'],
				click: function(){
					var cid = $( this ).find('#custom-id').val();
					$.ajax({
						dataType: "json",
						url: '../../load/custom_region_del.php',
						data: {
							cid: cid
						},
						type: "POST",
						success: function(data){
							if (data.success) {
								$( "#dialogCustomNote" ).dialog( "close");
								var element = $('div.p' + data.page);
								loadCustomRegions( data.page, element, data.cid);
							} else {
								$( "#dialogNotes" ).find('p').html( displayText['03'] );
								$( "#dialogNotes" ).dialog('option', 'title', displayText['11']);
								$( "#dialogNotes" ).dialog( "option", "modal", true );
								$( "#dialogNotes" ).dialog('open');
							}
						},
						error: function (xhr, ajaxOptions, thrownError) {
							$( "#dialogNotes" ).find('p').html( displayText['05'] );
							$( "#dialogNotes" ).dialog('option', 'title', displayText['11']);
							$( "#dialogNotes" ).dialog( "option", "modal", true );
							$( "#dialogNotes" ).dialog('open');
						}
					});

				}
			}
		],
		open: function( event, ui ) {
			$( this ).dialog( "option", "position", { my: "center", at: "center", of: window } );

			if ( customNoteAdd ) {
				$( this ).parent().find('button:first').find('span').text( displayText['09'] );
				$( this ).find('p').hide();
				$( this ).find('textarea').show();
			} else {
				$( this ).parent().find('button:first').find('span').text( displayText['08'] );
				$( this ).find('textarea').hide();
				$( this ).find('p').show();
			}
			$( this ).parent().find('button:first').focus();
			if ( usertype == 2 ) {
				$( this ).parent().find('button:nth-child(2)').hide();
			}			
		},
		beforeClose: function( event, ui ) {
			customNoteAdd = false;
			$( this ).dialog( "option", "modal", false );
		},
		create: function(event,ui) {
			$( "#dialogCustomNote" ).bind('touchend', function(e) {
				if ( !$(e.target).is("a") && !$(e.target).is("input") && !$(e.target).is("textarea") ) {
					e.preventDefault();
				}
			});
		}
	});

	$( "#dialogAudio" ).dialog({
 		autoOpen: false,
		width: 350,
		height: 100,
		resizable: false,
		modal: false,
		open: function( event, ui ) {
			$( this ).dialog( "option", "position", { my: "center", at: "center", of: window } );
			players[0].setVolume(5);
			players[0].play();
		},
		beforeClose: function( event, ui ) {
			players[0].stop();
		},
		create: function(event,ui) {
			$( "#dialogAudio" ).bind('click', function(e) {
				if ( !$(e.target).is("button") && !$(e.target).is("input") ) {
					e.preventDefault();
				}
			});
		}
	});

	$( "#dialogVideo" ).dialog({
 		autoOpen: false,
		width:  $(window).width() * 1,
		height: $(window).height() * 1,
		resizable: false,
		modal: true,
		open: function( event, ui ) {
			$( this ).dialog( "option", "position", { my: "center", at: "center", of: window } );
			document.getElementById("myVideo").volume = 0.5;
			document.getElementById("myVideo").play();
		},
		beforeClose: function( event, ui ) {
			document.getElementById("myVideo").pause();
		},
		create: function(event,ui) {
			$( "#dialogVideo" ).bind('touchend', function(e) {
				e.preventDefault();
			});
		}
	});

	$( "#dialogHtml5" ).dialog({
 		autoOpen: false,
		resizable: false,
		modal: true,
		open: function( event, ui ) {
			$( this ).dialog( "option", "position", { my: "center", at: "center", of: window } );
			var dWidth = $("#dialogHtml5").width() + 'px';
			var dHeight = $("#dialogHtml5").height() + 'px';
			$('#myHtml5').css('width',dWidth);
			$('#myHtml5').css('height',dHeight);
		},
		beforeClose: function( event, ui ) {
			$( "#dialogHtml5" ).attr('data-student','');
			$("#myHtml5").attr("src", "")
		},
		create: function(event,ui) {
			$( "#dialogHtml5" ).bind('touchend', function(e) {
				e.preventDefault();
			});
		}
	});
	
	$( "#dialogTest2" ).dialog({
 		autoOpen: false,
		resizable: false,
		modal: true,
		open: function( event, ui ) {
			$( this ).dialog( "option", "width", $(window).width() * 0.8 );
			$( this ).dialog( "option", "height", $(window).height() * 0.8 );
			$( this ).dialog( "option", "position", { my: "center", at: "center", of: window } );
			var dWidth = $("#dialogTest2").width() + 'px';
			var dHeight = $("#dialogTest2").height() + 'px';
			$('#testTable3').css('width',dWidth);
			$('#testTable3').css('height',dHeight);
			
			$("#testWarning2").text("資訊同步中...");
			$("#testWarning2").show();
			
			var page = $('#html5Page').val();
			var number = $('#html5Number').val();
			
			// load data
			$.ajax({
				// async: false,
				dataType: "json",
				type: 'POST',
				url: '/ebookshelf/load/getTests2.php',
				data: {
					'book_id': bookID,
					'page': page,
					'number': number,
				},
				beforeSend: function() {
					
				},
				success: function(response) {
					
					if (response.teacher) {
						
						$("#testWarning2").hide();
						// fill teacher data
						$('#testClass2').children().remove();
												
						jQuery.each(response.classes, function(i, item) {
							$("#testClass2").append('<option value="' + item.class_id + '">' + item.class_name + '</option>');
						});
						
						testClass2 = response.classes;
						
						if ( selectClass == '0' ) {
							selectClass = response.classes[0].class_id;
						}
						
						$("#testClass2").val(selectClass).change();

					}
					
				},
				error: function (xhr,status,error) {
					$("#testWarning2").text("同步失敗，請再嘗試。");
				}
			});
			
		},
		beforeClose: function( event, ui ) {
			if ( $('#html5Src').val() != "" ) {
				$("#myHtml5").attr('src', $('#html5Src').val() );
				$( "#dialogHtml5" ).attr('data-student','');
				$( "#dialogHtml5" ).dialog('open');
			} else {
				// closeDialog();
				$( "#dialogTest" ).dialog( "open" );
			}
		},
		create: function(event,ui) {
			$( "#dialogTest" ).bind('touchend', function(e) {
				e.preventDefault();
			});
		}
	});
			
			
	$( "#dialogTest" ).dialog({
 		autoOpen: false,
		resizable: false,
		modal: true,
		open: function( event, ui ) {
			$( this ).dialog( "option", "width", $(window).width() * 0.8 );
			$( this ).dialog( "option", "height", $(window).height() * 0.8 );
			$( this ).dialog( "option", "position", { my: "center", at: "center", of: window } );
			var dWidth = $("#dialogTest").width() + 'px';
			var dHeight = $("#dialogTest").height() + 'px';
			$('#testTable').css('width',dWidth);
			$('#testTable').css('height',dHeight);
			
			$("#testWarning").text("資訊同步中...");
			$("#testWarning").show();
			
			// load data
			$.ajax({
				// async: false,
				dataType: "json",
				type: 'POST',
				url: '/ebookshelf/load/getTests.php',
				data: {
					'book_id': bookID,
				},
				beforeSend: function() {
					
				},
				success: function(response) {
					if (response.student) {
						$("#testWarning").hide();
						// fill student data
						jQuery.each(response.rows, function(i, item) {
							// console.log(item.test_mark);
							$('#testTable2 tr').each(function() {
								// if ( ($( this ).find('td:nth-child(1)').attr('data') == item.page) && ($( this ).find('td:nth-child(2)').text() == item.number) ) {
								if ( parseInt($( this ).attr('data-page')) == item.page && parseInt($( this ).attr('data-number')) == item.number ) {
									if ( item.total_mark == 0 ) {
										if ( bookLang == 'eng' ) {
											$( this ).find('td:nth-child(4)').text( "submitted" );
										} else {
											$( this ).find('td:nth-child(4)').text( "已提交" );
										}
										$( this ).find('td:nth-child(4)').css("color","green");
									} else {
										$( this ).find('td:nth-child(4)').text( item.test_mark + ' / ' + item.total_mark );
										$( this ).find('td:nth-child(4)').css("color","green");
									}
								}
							});
						});
					} else if (response.teacher) {
						$("#testWarning").hide();
						// fill teacher data
						$('#testClass').children().remove();
												
						jQuery.each(response.classes, function(i, item) {
							$("#testClass").append('<option value="' + item.class_id + '">' + item.class_name + '</option>');
						});
						
						testClass = response.classes;
						
						if ( selectClass == '0' ) {
							selectClass = response.classes[0].class_id;
						}
						
						$("#testClass").val(selectClass).change();

						
					} else {
						$("#testWarning").text("同步失敗，請再嘗試。");
					}
				},
				error: function (xhr,status,error) {
					$("#testWarning").text("同步失敗，請再嘗試。");
				}
			});

			
			
		},
		beforeClose: function( event, ui ) {
			
		},
		create: function(event,ui) {
			$( "#dialogTest" ).bind('touchend', function(e) {
				e.preventDefault();
			});
			
		}
	});
	
	$('#testExcel').bind('click touchstart', function(e) {
		e.preventDefault();
		e.stopImmediatePropagation();
		var te = new TableExport(document.getElementById('testTable2'),{formats: ['xlsx'], filename: "resultCount", exportButtons: false, ignoreCols: 4});
		var exportData = te.getExportData()['testTable2']['xlsx'];
		console.log(exportData);
		te.export2file(exportData.data, exportData.mimeType, exportData.filename, exportData.fileExtension);
	});
	
	$('#testExcel2').bind('click touchstart', function(e) {
		e.preventDefault();
		e.stopImmediatePropagation();
		var te = new TableExport(document.getElementById('testTable4'),{formats: ['xlsx'], filename: "resultScore", exportButtons: false, ignoreCols: 6});
		var exportData = te.getExportData()['testTable4']['xlsx'];
		console.log(exportData);
		te.export2file(exportData.data, exportData.mimeType, exportData.filename, exportData.fileExtension);
	});
	
	$('select#testClass').change(function(){
		selectClass = $('#testClass').val();
		$.each(testClass, function(i, item) {
			if ( item.class_id == $('#testClass').val() ) {
				
				$('#testTable2 tr').each(function() {
					$( this ).find('td:nth-child(4)').text( '0 / ' + item.size ).css("color","blue");
				});
				
				jQuery.each(item.rows, function(i2, item2) {
					$('#testTable2 tr').each(function() {
						// if ( ($( this ).find('td:nth-child(1)').attr('data') == item2.page) && ($( this ).find('td:nth-child(2)').text() == item2.number) ) {
						if ( ( parseInt($( this ).attr('data-page')) == item2.page) && ( parseInt($( this ).attr('data-number')) == item2.number) ) {
							$( this ).find('td:nth-child(4)').text( item2.count + ' / ' + item.size );
							if ( item2.count == item.size ) {
								$( this ).find('td:nth-child(4)').css("color","green");
							}
						}
					});
				});
				
			}
		});
	});
	
	$('select#testClass2').change(function(){
		selectClass = $('#testClass').val();
		var page = $('#html5Page').val();
		var number = $('#html5Number').val();

		$.each(testClass2, function(i, item) {

			if ( item.class_id == $('#testClass2').val() ) {

				$('#testTable4').empty();
				if ( bookLang == 'eng' ) {
					$('#testTable4').append('<tr><th>Class</th><th>Login</th><th>Name</th><th>Page</th><th>Test</th><th> </th><th></th></tr>');
				} else {
					$('#testTable4').append('<tr><th>班級</th><th>登入</th><th>名稱</th><th>頁數</th><th>測驗</th><th> </th><th></th></tr>');
				}
				
				jQuery.each(item.students, function(i2, item2) {
					if ( bookLang == 'eng' ) {
						$('#testTable4').append('<tr><td class="tableexport-string" >' + item.class_name + '</td><td class="tableexport-string" >' + item2.student_id + '</td><td class="tableexport-string" >' + item2.student_name + '</td><td class="tableexport-string" >' + page + '</td><td class="tableexport-string" >' + number + '</td><td class="tableexport-string" style="color:blue;">not submitted</td><td class="tableexport-string" >---</td></tr>');
					} else {
						$('#testTable4').append('<tr><td class="tableexport-string" >' + item.class_name + '</td><td class="tableexport-string" >' + item2.student_id + '</td><td class="tableexport-string" >' + item2.student_name + '</td><td class="tableexport-string" >' + page + '</td><td class="tableexport-string" >' + number + '</td><td class="tableexport-string" style="color:blue;">未完成</td><td class="tableexport-string" >---</td></tr>');
					}
					
				});
				
				jQuery.each(item.results, function(i2, item2) {
					$('#testTable4 tr').each(function() {
						if ( ($( this ).find('td:nth-child(2)').text() == item2.student_id) ) {
							if ( item2.total_mark == 0 ) {
								$( this ).find('td:nth-child(6)').html( '<p style="max-width:350px;">' + item2['test_result'][0] + '</p>' );
							} else {
								$( this ).find('td:nth-child(6)').text( item2.test_mark + ' / ' + item2.total_mark );
							}
							$( this ).find('td:nth-child(6)').css("color","green");
							if ( bookLang == 'eng' ) {
								$( this ).find('td:nth-child(7)').html('<a class="test3" data-name="' + item2.student_name + '" data-src="html5/p' + pad(page,2) + pad2(number) + '" data-student="' + item2.student_id + '">View answers</a>');
							} else {
								$( this ).find('td:nth-child(7)').html('<a class="test3" data-name="' + item2.student_name + '" data-src="html5/p' + pad(page,2) + pad2(number) + '" data-student="' + item2.student_id + '">檢視答案</a>');
							}							
						}
					});
				});
				
			}			
		});
		
	});

 	$('#canvas').fadeIn(1000);

 	var flipbook = $('.magazine');

 	// Check if the CSS was already loaded

	if (flipbook.width()==0 || flipbook.height()==0) {
		setTimeout(loadApp, 10);
		return;
	}

	// Create the flipbook

	flipbook.turn({

			// Magazine width

			width: flipBookWidth,

			// Magazine height

			height: flipBookHeight,

			// Duration in millisecond

			duration: 1000,

			// Enables gradients

			gradients: true,

			// Auto center this flipbook

			autoCenter: true,

			// Elevation from the edge of the flipbook when turning a page

			elevation: 50,

			// The number of pages

			pages: flipBookPages,

			// display

			display: flipBookDisplay,

			// Events

			when: {
				turning: function(event, page, view) {

					var book = $(this),
					currentPage = book.turn('page'),
					pages = book.turn('pages');

					// Update the current URI

					Hash.go('page/' + page).update();

					// Show and hide navigation buttons

					disableControls(page);

				},

				turned: function(event, page, view) {

					// console.log('turned');
					disableControls(page);

					$(this).turn('center');

					// $('#slider').slider('value', getViewNumber($(this), page));
					// $('#slider').slider('value', page);

					if (page==1) {
						$(this).turn('peel', 'br');
					}

				},

				end: function(event, pageObject, turned){
					// console.log('end');
					$('#slider').slider('value', pageObject.next);
				},

				missing: function (event, pages) {

					// Add pages that aren't in the magazine

					for (var i = 0; i < pages.length; i++)
						addPage(pages[i], $(this));

				}
			}

	});

	// Zoom.js

	$('.magazine-viewport').zoom({
		flipbook: $('.magazine'),

		max: function() {
			return zoomMax;
		},
		// max: 2,

		when: {
			swipeLeft: function() {

				// $(this).zoom('flipbook').turn('next');
				// $('.magazine').turn('next');
				if ( $('.magazine').turn("display") == "single" ) {
					$('.magazine').turn('next').turn('stop');
				} else {
					$('.magazine').turn('next');
				}

			},

			swipeRight: function() {

				// $(this).zoom('flipbook').turn('previous');
				if ( $('.magazine').turn("display") == "single" ) {
					$('.magazine').turn('previous').turn('stop');
				} else {
					$('.magazine').turn('previous');
				}

			},

			resize: function(event, scale, page, pageElement) {

				if (scale==1)
					loadSmallPage(page, pageElement);
				else
					loadLargePage(page, pageElement);

			},

			zoomIn: function (event, scale) {

				$('#slider-bar').hide();
				$('.made').hide();
				$('.magazine').removeClass('animated').addClass('zoom-in');
				// $('.zoom-icon').removeClass('zoom-icon-in').addClass('zoom-icon-out');

				// 同步LiterallyCanvas的缩放
				syncLiterallyCanvasZoom();

				if ( !$.isTouch ) {

					$('<div />', {'class': 'exit-message'}).
						html('<div>Press ESC to exit</div>').
							appendTo($('body')).
							delay(2000).
							animate({opacity:0}, 500, function() {
								$(this).remove();
							});
				}
			},

			zoomOut: function (event, scale) {

				$('#slider-bar').fadeIn();
				$('.exit-message').hide();
				$('.made').fadeIn();
				// $('.zoom-icon').removeClass('zoom-icon-out').addClass('zoom-icon-in');

				// 同步LiterallyCanvas的缩放
				syncLiterallyCanvasZoom();

				setTimeout(function(){
					$('.magazine').addClass('animated').removeClass('zoom-in');
					if ( resized == 0 ) {
						resizeViewport();
					} else {
						if ( $(window).height() > $(window).width() ) {
							turnSingle(flipbook);
						} else {
							turnDouble(flipbook);
						}
						resized = 0;
					}
				}, 0);

			}
		}
	});

	// Zoom event

	if ($.isTouch) {
		$('.magazine-viewport').bind('zoom.doubleTap', zoomTo);
	} else {
		$('.magazine-viewport').bind('zoom.tap', zoomTo);
	}


	// Using arrow keys to turn the page

	$(document).keydown(function(e){

		var previous = 37, next = 39, esc = 27;

		switch (e.keyCode) {
			case previous:
				if ( !$('#dialogCustomNote').dialog('isOpen') ) {
					e.preventDefault();
					// left arrow
					if ( $('.magazine').turn("display") == "single" ) {
						$('.magazine').turn('previous').turn('stop');
					} else {
						$('.magazine').turn('previous');
					}
				}
			break;
			case next:
				if ( !$('#dialogCustomNote').dialog('isOpen') ) {
					e.preventDefault();
					//right arrow
					if ( $('.magazine').turn("display") == "single" ) {
						$('.magazine').turn('next').turn('stop');
					} else {
						$('.magazine').turn('next');
					}
				}
			break;
			case esc:
				e.preventDefault();
				$('.magazine-viewport').zoom('zoomOut');

			break;
		}
	});

	// URIs - Format #/page/1

	Hash.on('^page\/([0-9]*)$', {
		yep: function(path, parts) {
			var page = parts[1];

			if (page!==undefined) {
				if ($('.magazine').turn('is')) {
					if ( $('.magazine').turn("display") == "single" ) {
						$('.magazine').turn('page', page).turn('stop');
					} else {
						$('.magazine').turn('page', page);
					}
				}
			}

		},
		nop: function(path) {

			if ($('.magazine').turn('is'))
				$('.magazine').turn('page', 1);
		}
	});

	$(window).resize(function() {
		
		if( $(document.activeElement).prop('type') === 'textarea' || $(document.activeElement).prop('type') === 'text' || document.activeElement.tagName == 'IFRAME' ) {
			// do nothing 
		} else {
			if ( $('.magazine-viewport').zoom("value")>1 ) {
				resized = 1;
				$('.magazine-viewport').zoom('zoomOut');
			} else {
				if ( $(window).height() > $(window).width() ) {
					turnSingle(flipbook);
				} else {
					turnDouble(flipbook);
				}
			}
		}
	});

	// Regions

	if ($.isTouch) {
		$('.magazine').bind('touchstart click', regionClick);
	} else {
		$('.magazine').click(regionClick);
	}

	// Events for the next button

	$('.next-button').bind($.mouseEvents.over, function(event) {

		$(this).addClass('next-button-hover');

	}).bind($.mouseEvents.out, function(event) {

		$(this).removeClass('next-button-hover');

	}).bind($.mouseEvents.down, function(event) {

		$(this).addClass('next-button-down');

	}).bind($.mouseEvents.up, function(event) {

		$(this).removeClass('next-button-down');

	}).click(function(event) {

		event.preventDefault();

		if ( $('.magazine').turn("display") == "single" ) {
			$('.magazine').turn('next').turn('stop');
		} else {
			$('.magazine').turn('next');
		}

	});

	// Events for the previous button

	$('.previous-button').bind($.mouseEvents.over, function() {

		$(this).addClass('previous-button-hover');

	}).bind($.mouseEvents.out, function() {

		$(this).removeClass('previous-button-hover');

	}).bind($.mouseEvents.down, function() {

		$(this).addClass('previous-button-down');

	}).bind($.mouseEvents.up, function() {

		$(this).removeClass('previous-button-down');

	}).click(function(event) {

		event.preventDefault();

		if ( $('.magazine').turn("display") == "single" ) {
			$('.magazine').turn('previous').turn('stop');
		} else {
			$('.magazine').turn('previous');
		}
	});


	// Slider

	$( "#slider" ).slider({
		min: 1,
		max: numberOfViews(flipbook),

		start: function(event, ui) {

			if (!window._thumbPreview) {
				_thumbPreview = $('<div />', {'class': 'thumbnail'}).html('<div><span>10-101</span></div>');
				setPreview(ui.value);
				_thumbPreview.appendTo($(ui.handle));
			} else
				setPreview(ui.value);

			moveBar(false);

		},

		slide: function(event, ui) {

			setPreview(ui.value);

		},

		stop: function() {

			if (window._thumbPreview)
				_thumbPreview.removeClass('show');

			// $('.magazine').turn('page', Math.max(1, $(this).slider('value')*2 - 2));
			if ( $('.magazine').turn("display") == "single" ) {
				$('.magazine').turn('page', Math.max(1, $(this).slider('value') )).turn('stop');;
			} else {
				$('.magazine').turn('page', Math.max(1, $(this).slider('value') ));
			}

		}
	});

	resizeViewport();

	$('.magazine').addClass('animated');


	// Zoom icon

	 $('.zoom-icon').bind('mouseover', function() {

		if ($(this).hasClass('zoom-icon-in'))
			$(this).addClass('zoom-icon-in-hover');

		if ($(this).hasClass('zoom-icon-out'))
			$(this).addClass('zoom-icon-out-hover');

	 }).bind('mouseout', function() {

		 if ($(this).hasClass('zoom-icon-in'))
			$(this).removeClass('zoom-icon-in-hover');

		if ($(this).hasClass('zoom-icon-out'))
			$(this).removeClass('zoom-icon-out-hover');

	 }).bind('click touchstart', function() {

		if ($(this).hasClass('zoom-icon-in')) {

			var currentZoom = $('.magazine-viewport').zoom("value");
			var targetZoom;

			// Define smooth zoom levels progression
			if (currentZoom == 1) {
				targetZoom = 1.5;
			} else if (currentZoom == 1.5) {
				targetZoom = 2;
			} else if (currentZoom == 2) {
				targetZoom = 2.5;
			} else {
				targetZoom = 1; // Reset to normal zoom
			}

			// Use the new smooth zoomTo method
			$('.magazine-viewport').zoom('zoomTo', targetZoom);

		} else if ($(this).hasClass('zoom-icon-out')) {
			// Smooth zoom out to normal level
			$('.magazine-viewport').zoom('zoomTo', 1);
		}

		// 确保LiterallyCanvas缩放同步 - increased timeout for smooth transition
		setTimeout(function() {
			syncLiterallyCanvasZoom();
		}, 650); // Slightly longer than zoom duration

	 });

	// page icon single page double page
	 $('.page-icon').bind('click touchstart', function() {

		// 移除缩放限制，允许在任何缩放级别下切换页面模式
		if ($(this).hasClass('page-icon-double')) {
			turnSingle(flipbook);
		} else if ($(this).hasClass('page-icon-single')) {
			turnDouble(flipbook);
		}

	 });

	 // go back to ebookshelf
	 $('.home-icon').bind('click touchstart', function() {
		window.location = "../../";
	 });

	 // go to table of content page
	 $('.content-icon').bind('click touchstart', function() {
		$('.magazine').turn('page', contentPage);
	 });
	 
	// drawing board funtions
	$('#pen1').bind('click touchstart', function() {
		lc.colors.primary = 'hsla(240, 100%, 50%, 1)';
		lc.setTool(lcPen);
		$('.lc-pen').css('background-position','-20px 0');
		$(this).css('background-position','0 0');
	});	
	
	$('#pen2').bind('click touchstart', function() {
		lc.colors.primary = 'hsla(0, 100%, 50%, 1)';
		lc.setTool(lcPen);
		$('.lc-pen').css('background-position','-20px 0');
		$(this).css('background-position','0 0');
	});	
	
	$('#pen3').bind('click touchstart', function() {
		lc.colors.primary = 'hsla(60, 100%, 50%, 0.5)';
		lc.setTool(lcHighlighter);
		$('.lc-pen').css('background-position','-20px 0');
		$(this).css('background-position','0 0');
	});	
	
	$('#eraser').bind('click touchstart', function() {
		lc.setTool(lcEraser);
		$('.lc-pen').css('background-position','-20px 0');
		$(this).css('background-position','0 0');
	});	
	
	$('#lc-clear').bind('click touchstart', function() {
		lc.clear();
	});	
	
	$('#lc-undo').bind('click touchstart', function() {
		lc.undo();
	});	
	
	$('#lc-redo').bind('click touchstart', function() {
		lc.redo();
	});	
	
	$('.highlighter-icon').bind('click touchstart', function() {
		
		// 移除缩放限制，允许在任何缩放级别下使用画笔
		closeDialog();
		
		if ( $('.page-icon').hasClass('page-icon-double') ) {
			turnSingle(flipbook);
		}

		var currentPage = $(".magazine").turn("page");
		var winHeight = $( '.magazine' ).height();
		var winWidth = $( '.magazine' ).width();
		
		$( "#lc" ).width( winWidth + 80 );
		$( "#lc" ).height( winHeight );
		
		$( "#lc" ).css('top',  $( '.magazine' ).offset().top );
		$( "#lc" ).css('left', $( '.magazine' ).offset().left );

		$( "#lc" ).css('background-image','url(pages/' + currentPage + '.jpg)');
		
		$( "#lc" ).modal({
			escapeClose: false,
			clickClose: false
		});
		
		saveRegionLog(bookID,'click on custom draw', currentPage ,'custom-draw');
	});

	$('#lc').on($.modal.BEFORE_CLOSE, function(event, modal) {
		// save data
		var currentPage = $(".magazine").turn("page");
		if ( $(".p" + currentPage).find('.custom-draw').length == 1  ) {
			var id = $(".p" + currentPage).find('.custom-draw').attr('region-id');
			if ( lc.getSnapshot().shapes.length > 0 ) {
				// update
				saveCanvas(2,id,lc.getImage().toDataURL(),JSON.stringify(lc.getSnapshot()));
				
			} else {
				// delete
				saveCanvas(3,id,'','');
			}
		} else {
			if ( lc.getSnapshot().shapes.length > 0 ) {
				// add new
				saveCanvas(1,0,lc.getImage().toDataURL(),JSON.stringify(lc.getSnapshot()));
			}
		}
		lc.teardown();
	});

	$('#lc').on($.modal.OPEN, function(event, modal) {
		
		var currentPage = $(".magazine").turn("page");
		var winHeight = $( '.magazine' ).height();
		var winWidth = $( '.magazine' ).width();
		
		lc = LC.init(document.getElementById('lc-canvas'),{
			keyboardShortcuts: false,
			imageSize: {width: flipBookSingleWidth, height: flipBookHeight},
			tools: [LC.tools.Pencil, LC.tools.Eraser, LC.tools.Line]
		});
		
		lcPen = new LC.tools.Pencil(lc);
		lcPen.strokeWidth = 3;
		
		lcEraser = new LC.tools.Eraser(lc);
		lcEraser.strokeWidth = 25;
		
		lcHighlighter = new LC.tools.Line(lc);
		lcHighlighter.strokeWidth = 15;
		
		$('.lc-pen').css('background-position','-20px 0');
		$('#pen3').css('background-position','0 0');
		
		if ( $(".p" + currentPage).find('.custom-draw').length == 1  ) {
			lc.loadSnapshot( JSON.parse( $(".p" + currentPage).find('.custom-draw').attr('region-object') ) );
		}

		lc.setTool(lcHighlighter);
		lc.setColor('primary', 'hsla(60, 100%, 50%, 0.5)');
		
		// 同步当前的缩放值到LiterallyCanvas
		syncLiterallyCanvasZoom();

	});

	// bookmark funtions
	$('.bookmark-icon').bind('click touchstart', function() {
		closeDialog();
		$( "#dialogBookmark" ).dialog( "open" );
	});
	
	$('.test-icon').bind('click touchstart', function() {
		closeDialog();
		$( "#dialogTest" ).dialog( "open" );
	});
	
	$('#bookmark-del').bind('click touchstart', function() {
		var currentPage = $(".magazine").turn("page");
		if ( currentPage in bookmark ) {
			delete bookmark[currentPage];
			saveBookmark();
		}
	});

	$('#bookmark-add').bind('click touchstart', function() {
		var currentPage = $(".magazine").turn("page");
		if ( !(currentPage in bookmark) ) {
			bookmark[currentPage] = pageName[currentPage];
			saveBookmark();
		}
	});

	// $('#file-upload').bind('click touchstart', function() {

	$('#bookmark-content').delegate( '.bookmark-to-page', 'click touchstart', function() {
		if ( $('.magazine').turn("display") == "single" ) {
			$('.magazine').turn('page', $(this).attr('data-page') ).turn('stop');;
		} else {
			$('.magazine').turn('page', $(this).attr('data-page') );
		}
		closeDialog();
	});

	$('#dialogTest').on('click touchstart', 'a.test2', function() {
		closeDialog();
		var page = $(this).parent().parent().attr('data-page');
		var number = $(this).parent().parent().attr('data-number');
		$('#html5Src').val('');
		$('#html5Page').val(page);
		$('#html5Number').val(number);
		$('#dialogTest2').dialog('open');
	});
	
	$('#dialogTest2').on('click touchstart','a.test3', function() {
		var title = "檢視答案 - " + $(this).attr('data-student');
		var src = "../../book/" + bookPath + "/" + $(this).attr('data-src');
		var student = $(this).attr('data-student');
		$("#myHtml5").attr('src',src);
		$( "#dialogHtml5" ).dialog('option', 'title', title);
		
		var adjustPercent = 0.8; // percentage default 0.95
		var adjustTitle = 20;  // pixels default 33
		
		if ( $(window).width() / $(window).height() > 1.33 ) {
			var wHeight = parseInt( $(window).height() * adjustPercent ) -adjustTitle ;
			$( "#dialogHtml5" ).dialog('option', 'width', parseInt( (wHeight - adjustTitle) * 1.33) );
			$( "#dialogHtml5" ).dialog('option', 'height', wHeight );
		} else {
			$( "#dialogHtml5" ).dialog('option', 'width', parseInt($(window).width() * adjustPercent ) );
			$( "#dialogHtml5" ).dialog('option', 'height', parseInt($(window).width() * adjustPercent * 0.75) + adjustTitle );
		}
		
		$( "#dialogHtml5" ).attr('data-student',student);
		$( "#dialogHtml5" ).dialog('open');
	});
	
	$('a.testHtml5').bind('click touchstart', function() {
		closeDialog();
		var title = $(this).text();
		if ( $(this).parent().parent().attr('data-number') == '1' ) {
			var filename = 'p' + $(this).parent().parent().attr('data-page');
		} else {
			var filename = 'p' + $(this).parent().parent().attr('data-page') + '-' + $(this).parent().parent().attr('data-number');
		}
		
		document.getElementById("myHtml5").src = "../../book/" + bookPath + "/html5/" + filename + "/";
		$( "#dialogHtml5" ).dialog('option', 'title', title);
		
		var adjustPercent = 1; // percentage default 0.95
		var adjustTitle = 20;  // pixels default 33
		
		if ( $(window).width() / $(window).height() > 1.33 ) {
			var wHeight = parseInt( $(window).height() * adjustPercent ) -adjustTitle ;
			$( "#dialogHtml5" ).dialog('option', 'width', parseInt( (wHeight - adjustTitle) * 1.33) );
			$( "#dialogHtml5" ).dialog('option', 'height', wHeight );
		} else {
			$( "#dialogHtml5" ).dialog('option', 'width', parseInt($(window).width() * adjustPercent ) );
			$( "#dialogHtml5" ).dialog('option', 'height', parseInt($(window).width() * adjustPercent * 0.75) + adjustTitle );
		}
		
		$( "#dialogHtml5" ).attr('data-student','');
		$( "#dialogHtml5" ).dialog('open');
		
	});
	
	
	$('.note-icon').bind('click touchstart', function() {
		// console.log( $('.page').css('cursor') );
		if ( $('.page').css('cursor') == 'auto' || $('.page').css('cursor') == 'default') {
			disableZoom = true;
			$('#slider-bar').hide();
			$('.next-button').hide();
			$('.previous-button').hide();
			$('.magazine').turn("disable", true);
			$('.page').css('cursor','url(../../pics/note-3-icons.png),context-menu');
			$('.page').bind('click',function(event){

				if ( $(event.target).hasClass('region') || $(event.target).hasClass('custom') ) {
					event.preventDefault;
				} else {
					// add new notes to database
					customNoteAdd = true;
					var page = $.trim($(event.delegateTarget).attr('class').replace('page', '').replace('odd', '').replace('even', '').replace('p', ''));
					var element = event.delegateTarget;
					$.ajax({
						dataType: "json",
						url: '../../load/custom_region_add.php',
						data: {
							book_id:bookID,
							page:page,
							class:'custom-note',
							x: Math.round( event.offsetX / event.delegateTarget.offsetWidth * 1000) / 10 + '%',
							y: Math.round( event.offsetY / event.delegateTarget.offsetHeight * 1000) / 10 + '%',
							width: '5%',
							height: '5%'
						},
						type: "POST",
						success: function(data){
							// unbind click and return to normal
							disableZoom = false;
							$('#slider-bar').fadeIn();
							$('.next-button').show();
							$('.previous-button').show();
							$('.page').css('cursor','auto');
							$('.magazine').turn("disable", false);
							$('.page').unbind('click');

							if (data.success) {
								loadCustomRegions(page, element, data.new_id);
							}
						}
					});
				}

			});
		} else {
			disableZoom = false;
			customNoteAdd = false;
			$('#slider-bar').fadeIn();
			$('.next-button').show();
			$('.previous-button').show();
			$('.page').css('cursor','auto');
			$('.magazine').turn("disable", false);
			$('.page').unbind('click');
		}
	});

}

function openTestDialog2(src,page,number) {
	$( ".eBookDialog" ).each( function() {
		if ($(this).dialog('isOpen') === true) {
			$(this).dialog('close');
		}
	} );
	$('#html5Src').val(src);
	$('#html5Page').val(page);
	$('#html5Number').val(number);
	$('#dialogTest2').dialog('open');
}

function pad(num, size) {
    var s = num+"";
    while (s.length < size) s = "0" + s;
    return s;
}

function pad2(num) {
	if ( num == 1 ) {
		return "";
    } else {
		return "-" + num;
	}
}