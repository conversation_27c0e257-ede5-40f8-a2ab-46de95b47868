﻿/*
 * Magazine - ebookshelf 2022-06-10
*/
String.prototype.replaceAll = function(search, replacement) {
    var target = this + '';
    return target.split(search).join(replacement);
};

function addPage(page, book) {

	var id, pages = book.turn('pages');

	// Create a new element for this page
	var element = $('<div />', {});

	// Add the page to the flipbook
	if (book.turn('addPage', element, page)) {

		// Add the initial HTML
		// It will contain a loader indicator and a gradient
		element.html('<div class="gradient"></div><div class="loader"></div>');

		// Load the page
		loadPage(page, element);
	}

}

function loadPage(page, pageElement) {

	// Create an image element

	var img = $('<img />');

	img.mousedown(function(e) {
		e.preventDefault();
	});

	img.load(function() {
		
		// Set the size
		$(this).css({width: '100%', height: '100%'});

		// Add the image to the page after loaded

		$(this).appendTo(pageElement);

		// Remove the loader indicator
		
		pageElement.find('.loader').remove();
	});

	// Load the page

	img.attr('src', 'pages/' +  page + '.jpg');

	loadRegions(page, pageElement);
	loadCustomRegions(page, pageElement, 0);
	
}

// Zoom in / Zoom out - Updated for smooth transitions

function zoomTo(event) {

	console.log('magazine.js zoomTo called with event:', event);

	if ( disableZoom ) {
		console.log('Zoom disabled, ignoring zoomTo call');
	} else {
		setTimeout(function() {
			if ($('.magazine-viewport').data().regionClicked) {
				console.log('Region was clicked, ignoring zoom');
				$('.magazine-viewport').data().regionClicked = false;
			} else {
				var currentZoom = $('.magazine-viewport').zoom('value');
				console.log('magazine.js - Current zoom:', currentZoom);
				var targetZoom;

				// Define smooth zoom levels progression
				if (currentZoom == 1) {
					targetZoom = 1.5;
				} else if (currentZoom == 1.5) {
					targetZoom = 2;
				} else if (currentZoom == 2) {
					targetZoom = 2.5;
				} else {
					targetZoom = 1; // Reset to normal zoom
				}

				console.log('magazine.js - Target zoom:', targetZoom);

				// Try the new smooth zoomTo method first, fallback to original if it fails
				try {
					$('.magazine-viewport').zoom('zoomTo', targetZoom, event);
				} catch (error) {
					console.error('magazine.js zoomTo failed, falling back to original method:', error);
					// Fallback to original method
					zoomMax = targetZoom;
					if (targetZoom === 1) {
						$('.magazine-viewport').zoom('zoomOut');
					} else {
						$('.magazine-viewport').zoom('zoomIn', event);
					}
				}

				// 同步LiterallyCanvas缩放（如果存在）- increased timeout for smooth transition
				setTimeout(function() {
					if (typeof syncLiterallyCanvasZoom === 'function') {
						syncLiterallyCanvasZoom();
					}
				}, 650); // Slightly longer than zoom duration
			}
		}, 1);
	}
}

// load custom regions
function loadCustomRegions(page, element, id) {

		var myNote = '我的筆記';
		if ( bookLang == 'eng' ) myNote = 'My Note';

		$.ajax({
			dataType: "json",
			url: '../../load/custom_region.php',
			data: {
				book_id:bookID,
				page:page
			},
			type: "POST",
			success: function(data){
				
				$(element).find('div.custom').remove();
				
				$.each(data, function(key, region) {
					// addRegion(region, element);
					// console.log(region);
					switch (region['class']) {
						case 'custom-note':
							var reg = $('<div />', {'class': 'custom  ' + region['class']});
							reg.css({
								top: region.y,
								left: region.x,
								width: region.width,
								height: region.height,
								'background-image': 'url(icon/note-2-icons.png)'
							}).attr('region-data', $.param(region.data||'')).attr('title',myNote).attr('region-id',region.custom_id);
							reg.appendTo(element);
							break;
						case 'custom-draw':
							var reg = $('<div />', {'class': 'custom  ' + region['class']});
							reg.css({
								top: region.y,
								left: region.x,
								width: region.width,
								height: region.height,
								'background-image': 'url(' + region.data.content + ')'
							}).attr('region-data','').attr('region-object', JSON.stringify(region.data.object) ).attr('region-id',region.custom_id);
							reg.prependTo(element);
							break;
					}
					
				});
				
				if ( id > 0 ) {
					$(element).find('div.custom[region-id="' + id + '"]').click();
				}
				
			},
			error: function (xhr, ajaxOptions, thrownError) {
				console.log(xhr.status);
				console.log(thrownError);
				console.log(xhr.responseText);
			}
		});

}


// Load regions

function loadRegions(page, element) {

	$.getJSON('pages/'+page+'-regions.json?nocache='  + (new Date()).getTime() ).
		done(function(data) {

			$.each(data, function(key, region) {
				addRegion(region, element);
			});
		});
}

// Add region

function addRegion(region, pageElement) {
	
	var reg = $('<div />', {'class': 'region  ' + region['class']}),
		options = $('.magazine').turn('options'),
		pageWidth = options.width/2,
		pageHeight = options.height;
	
	// if ( $('.magazine').turn("display") == "single" ) {
		// pageWidth = options.width;
	// }

	reg.css({
		top: Math.round( region.y/pageHeight*1000 ) / 10 +'%',
		left: Math.round( region.x/pageWidth*1000 ) / 10 +'%',
		width: Math.round( region.width/pageWidth*1000 ) / 10 +'%',
		height: Math.round( region.height/pageHeight*1000 ) / 10 +'%'
	}).attr('region-data', $.param(region.data||'')).attr('title',region['title']);
	
	switch (region['class']) {
		case 'audio':
			if ( region['icon'] == 'none' ) {
				reg.addClass('audio2');
			} else {
				reg.css('background-image','url(icon/' + region['icon'] + ')');
			}
			break;
		case 'video':
			reg.css('background-image','url(icon/' + region['icon'] + ')');
			break;
		case 'html5':
			reg.css('background-image','url(icon/' + region['icon'] + ')');
			break;
		case 'cover':
			reg.css('background',region['color']);
			break;			
		case 'notes':
			reg.css('background-image','url(icon/' + region['icon'] + ')');
			break;
		case 'youtube':
			reg.css('background-image','url(icon/' + region['icon'] + ')');
			break;
		case 'answer':
			if ( region['show'] == 1 ) {
				reg.css('background-image','url(ans/' + region.data.filename + ')');
				reg.addClass('answer2');
			}
			break;
	}
	
	reg.appendTo(pageElement);
}

// Process click on a region

function regionClick(event) {
	
	// event.stopPropagation();

	var region = $(event.target);
	if (region.hasClass('custom-note')) {
		
		event.preventDefault();
		event.stopPropagation();
		
		$('.magazine-viewport').data().regionClicked = true;
		
		setTimeout(function() {
			$('.magazine-viewport').data().regionClicked = false;
		}, 100);
		
		var regionType = $.trim(region.attr('class').replace('custom', ''));
		
		return processRegion(region, regionType);
	}
	
	if (region.hasClass('region')) {
		
		event.preventDefault();
		event.stopPropagation();

		$('.magazine-viewport').data().regionClicked = true;
		
		setTimeout(function() {
			$('.magazine-viewport').data().regionClicked = false;
		}, 100);
		
		var regionType = $.trim(region.attr('class').replace('region', '').replace('answer2','').replace('audio2',''));
		
		// region.hide().delay(1000).show();
		
		return processRegion(region, regionType);

	}

}

// Process the data of every region

function processRegion(region, regionType) {

	data = decodeParams(region.attr('region-data'));
	var log = "";
	var page = $(".magazine").turn("page");
	var title = "";
	var cid = "";

	switch (regionType) {
		case 'link' :

			window.open(data.url);
			log = data.url;

		break;
		case 'youtube' :

			window.open(data.url);
			log = data.url;

		break;
		case 'zoom' :

			var regionOffset = region.offset(),
				viewportOffset = $('.magazine-viewport').offset(),
				pos = {
					x: regionOffset.left-viewportOffset.left,
					y: regionOffset.top-viewportOffset.top
				};

			$('.magazine-viewport').zoom('zoomIn', pos);

		break;
		case 'to-page' :

			$('.magazine').turn('page', data.page);
			log = data.page;

		break;
		case 'cover' :

			if ( region.css('opacity') == 1 ) {
				region.css('opacity', 0);
			} else {
				region.css('opacity', 1);
			}

		break;
		case 'audio' :
			title = region.attr('title');
			
			closeDialog();
			
			document.getElementById("mp3_src").src = "../../book/" + bookPath + "/audio/" + data.filename;
			document.getElementById("myAudio").load();

			
			$( "#dialogAudio" ).dialog('option', 'title', title);
			$( "#dialogAudio" ).dialog('open');
			
			log = data.filename;
			
		break;
		case 'video' :
			title = region.attr('title');
			
			closeDialog();
			
			if ( data.filename.startsWith("http") ) {
				document.getElementById("mp4_src").src = data.filename;
			} else {
				document.getElementById("mp4_src").src = "../../book/" + bookPath + "/video/" + data.filename;
			}
			
			document.getElementById("myVideo").load();
			
			$( "#dialogVideo" ).dialog('option', 'title', title);
			$( "#dialogVideo" ).dialog('option', 'width', parseInt($(window).width() * 1 ) );
			$( "#dialogVideo" ).dialog('option', 'height', parseInt($(window).height() * 1 ) );
			$( "#dialogVideo" ).dialog('open');
			
			log = data.filename;
			
		break;
		case 'html5' :
			title = region.attr('title');

			closeDialog();
			
			document.getElementById("myHtml5").src = "../../book/" + bookPath + "/html5/" + data.filename + "/";
			$( "#dialogHtml5" ).dialog('option', 'title', title);
			
			var adjustPercent = 1; // percentage default 0.95
			var adjustTitle = 20;  // pixels default 33
			
			if ( $(window).width() / $(window).height() > 1.33 ) {
				var wHeight = parseInt( $(window).height() * adjustPercent ) -adjustTitle ;
				$( "#dialogHtml5" ).dialog('option', 'width', parseInt( (wHeight - adjustTitle) * 1.33) );
				$( "#dialogHtml5" ).dialog('option', 'height', wHeight );
				$( "#dialogHtml5" ).dialog('open');
			} else {
				$( "#dialogHtml5" ).dialog('option', 'width', parseInt($(window).width() * adjustPercent ) );
				$( "#dialogHtml5" ).dialog('option', 'height', parseInt($(window).width() * adjustPercent * 0.75) + adjustTitle );
				$( "#dialogHtml5" ).dialog('open');
			}
			
			log = data.filename;
			
		break;
		case 'notes':
			title = region.attr('title');

			closeDialog();

			$( "#dialogNotes" ).find('p').html( nl2br( data.content.replaceAll('+',' ') ) );
			
			$( "#dialogNotes" ).dialog('option', 'title', title);
			$( "#dialogNotes" ).dialog('option', 'width', parseInt($(window).width() * 0.4 ) );
			$( "#dialogNotes" ).dialog('open');
			
			log = title + ' ' + data.content.replaceAll('+',' ').substring(0,20);
			
		break;
		case 'custom-note':
			title = region.attr('title');
			cid = region.attr('region-id');
			closeDialog();
			
			var linkedText = Autolinker.link( nl2br( data.content.replaceAll('+',' ') ), {
				hashtag: 'twitter',
				email: false,
				phone: false,
				replaceFn : function( match ) {
					// console.log( "href = ", match.getAnchorHref() );
					// console.log( "text = ", match.getAnchorText() );
					
					switch( match.getType() ) {
						case 'hashtag' :
							var hashtag = match.getHashtag();
							// console.log( hashtag );
							if ( hashtag.includes("file") ) {
								return '<a href="../../load/files.php?id=' + hashtag.replace('file','') + '" target="_blank">#' + hashtag + '</a>';
							} else {
								return '#' + hashtag;
							}
							break;
					}
				}				
			});
			
			$( "#dialogCustomNote" ).find('p').html( linkedText );
			
			$( "#dialogCustomNote" ).find('textarea').val( data.content.replaceAll('+',' ') );
			
			$( "#dialogCustomNote" ).find('#custom-id').val( cid );
			
			$( "#dialogCustomNote" ).dialog('option', 'title', title);
			
			if ( customNoteAdd ) {
				$( "#dialogCustomNote" ).dialog( "option", "modal", true );
			}
			
			$( "#dialogCustomNote" ).dialog('open');
			
			log = title + ' ' + data.content.replaceAll('+',' ').substring(0,20) + ' (' + cid + ')';
			
		break;
		case 'answer':
			
			if ( region.css('background-image') == 'none' ) {
				region.css('background-image','url(ans/' + data.filename + ')');
				region.addClass('answer2');
				
			} else {
				region.css('background-image','none');
				region.removeClass('answer2');
			}
			log = data.filename;
		break;
	}
	
	saveRegionLog(bookID,log,page,regionType);

}

// Load large page

function loadLargePage(page, pageElement) {
	
	var img = $('<img />');

	img.load(function() {

		var prevImg = pageElement.find('img');
		$(this).css({width: '100%', height: '100%'});
		$(this).appendTo(pageElement);
		prevImg.remove();
		
	});

	// Loadnew page
	
	img.attr('src', 'pages/' +  page + '-large.jpg');
}

// Load small page

function loadSmallPage(page, pageElement) {
	
	var img = pageElement.find('img');

	img.css({width: '100%', height: '100%'});

	img.unbind('load');
	// Loadnew page

	img.attr('src', 'pages/' +  page + '.jpg');
}

// http://code.google.com/p/chromium/issues/detail?id=128488

function isChrome() {

	return navigator.userAgent.indexOf('Chrome')!=-1;

}

function disableControls(page) {
		if (page==1)
			$('.previous-button').hide();
		else
			$('.previous-button').show();
					
		if (page==$('.magazine').turn('pages'))
			$('.next-button').hide();
		else
			$('.next-button').show();
}

// Set the width and height for the viewport

function resizeViewport() {
	
	// close all dialog if open
	// closeDialog();
	closeDialog2();

	var width = $(window).width(),
		height = $(window).height(),
		// options = $('.magazine').turn('options');
		options = $('.magazine').turn('size');

	$('.magazine').removeClass('animated');

	$('.magazine-viewport').css({
		width: width,
		height: height
	}).
	zoom('resize');


	if ($('.magazine').turn('zoom')==1) {
		var bound = calculateBound({
			width: options.width,
			height: options.height,
			// boundWidth: Math.min(options.width, width),
			// boundHeight: Math.min(options.height, height)
			boundWidth: width,
			boundHeight: height
		});

		if (bound.width%2!==0)
			bound.width-=1;

			
		if (bound.width!=$('.magazine').width() || bound.height!=$('.magazine').height()) {

			$('.magazine').turn('size', bound.width, bound.height);

			if ($('.magazine').turn('page')==1)
				$('.magazine').turn('peel', 'br');

			// $('.next-button').css({height: bound.height, backgroundPosition: '-38px '+(bound.height/2-32/2)+'px'});
			// $('.previous-button').css({height: bound.height, backgroundPosition: '76px '+(bound.height/2-32/2)+'px'});
			
		} 
		
			$('.next-button').css({height: bound.height, backgroundPosition: '-38px '+(bound.height/2-32/2)+'px'});
			$('.previous-button').css({height: bound.height, backgroundPosition: '76px '+(bound.height/2-32/2)+'px'});

		$('.magazine').css({top: -bound.height/2, left: -bound.width/2});
	}

	var magazineOffset = $('.magazine').offset(),
		boundH = height - magazineOffset.top - $('.magazine').height(),
		marginTop = (boundH - $('.thumbnails > div').height()) / 2;

	if (marginTop<0) {
		$('.thumbnails').css({height:1});
	} else {
		$('.thumbnails').css({height: boundH});
		$('.thumbnails > div').css({marginTop: marginTop});
	}

	if (magazineOffset.top<$('.made').height())
		$('.made').hide();
	else
		$('.made').show();

	$('.magazine').addClass('animated');
	
	
}


// Number of views in a flipbook

function numberOfViews(book) {
	// return book.turn('pages') / 2 + 1;
	return book.turn('pages');
}

// Current view in a flipbook

function getViewNumber(book, page) {
	return parseInt((page || book.turn('page'))/2 + 1, 10);
}

function moveBar(yes) {
	if (Modernizr && Modernizr.csstransforms) {
		$('#slider .ui-slider-handle').css({zIndex: yes ? -1 : 10000});
	}
}

function setPreview(view) {

	var previewWidth = 112,
		previewHeight = 73,
		previewSrc = 'pages/preview-single.jpg',
		preview = $(_thumbPreview.children(':first')),
		// numPages = (view==1 || view==$('#slider').slider('option', 'max')) ? 1 : 2,
		numPages = 1,
		width = (numPages==1) ? previewWidth/2 : previewWidth;

	_thumbPreview.
		addClass('no-transition').
		css({width: width + 15,
			height: previewHeight + 15,
			top: -previewHeight - 30,
			left: ($($('#slider').children(':first')).width() - width - 15)/2
		});

	preview.css({
		width: width,
		height: previewHeight
	});

	if (preview.css('background-image')==='' ||
		preview.css('background-image')=='none') {

		preview.css({backgroundImage: 'url(' + previewSrc + ')'});

		setTimeout(function(){
			_thumbPreview.removeClass('no-transition');
		}, 0);

	}

	preview.css({backgroundPosition:
		'0px -'+((view-1)*previewHeight)+'px'
	});
	
	// var pageNo = (view-1)*2;
	// var pageNo = view;
	// pageNo = (pageNo==0) ? 1 : pageNo;
	// pageNo = (numPages==1) ? pageNo : pageNo + ' - ' + (pageNo+1);
	var pageNo = pageName[view];
	preview.find('span').text( pageNo );
	
}

// Width of the flipbook when zoomed in

function largeMagazineWidth() {
	
	return 2214;

}

// decode URL Parameters

function decodeParams(data) {

	var parts = data.split('&'), d, obj = {};

	for (var i =0; i<parts.length; i++) {
		d = parts[i].split('=');
		// obj[decodeURIComponent(d[0])] = decodeURIComponent(d[1]);
		obj[decodeURIComponent(d[0])] = decodeURIComponent(d[1]);
	}

	return obj;
}

// Calculate the width and height of a square within another square

function calculateBound(d) {
	
	var bound = {width: d.width, height: d.height};

	if (bound.width>d.boundWidth*0.9 || bound.height>d.boundHeight*0.9 ) {
		
		var rel = bound.width/bound.height;

		if (d.boundWidth/rel>d.boundHeight && d.boundHeight*rel<=d.boundWidth) {
			bound.width = Math.round(d.boundHeight*rel);
			bound.height = d.boundHeight;
		} else {			
			bound.width = d.boundWidth;
			bound.height = Math.round(d.boundWidth/rel);
		}
		
		bound.width = bound.width * 0.9;
		bound.height = bound.height * 0.9;
		
	}
	
	return bound;
}

function turnSingle(book) {
	book.turn("display", "single");
	book.turn("size", flipBookSingleWidth, flipBookHeight);
	$('.page-icon').removeClass('page-icon-double').addClass('page-icon-single');
	resizeViewport();
}

function turnDouble(book) {
	book.turn("display", "double");
	book.turn("size", flipBookWidth,  flipBookHeight);
	$('.page-icon').removeClass('page-icon-single').addClass('page-icon-double');
	resizeViewport();
}

function saveRegionLog(bookID,log,page,className) {
	$.ajax({
		dataType: "json",
		url: '../../load/region_log.php',
		data: {
			book_id:bookID,
			log:log,
			page:page,
			classname:className
		},
		type: "POST",
		success: function(data){
			// console.log('Log OK.');
		}
	});
}

function saveCanvas(type,id,url,object) {
	var currentPage = $(".magazine").turn("page");
	$.ajax({
		dataType: "json",
		url: '../../load/canvas_save.php',
		data: {
			type:type,
			custom_id:id,
			book_id:bookID,
			className:'custom-draw',
			page:currentPage,
			data:url,
			data2:object
		},
		type: "POST",
		success: function(data){
			// console.log('Log OK.');
			var element = $('div.p' + data.page);
			loadCustomRegions( data.page, element, 0);
		}
	});	
}

function nl2br (str, is_xhtml) {   
    var breakTag = (is_xhtml || typeof is_xhtml === 'undefined') ? '<br />' : '<br>';    
    return (str + '').replace(/([^>\r\n]?)(\r\n|\n\r|\r|\n)/g, '$1'+ breakTag +'$2');
}

function closeDialog() {
	$( ".eBookDialog" ).each( function() {
		if ($(this).dialog('isOpen') === true ) {
			$(this).dialog('close');
		}
	} );
}

function closeDialog2() {
	$( ".eBookDialog" ).each( function() {
		// dialogAudio
		if ($(this).dialog('isOpen') === true && $(this).attr('id') != "dialogAudio") {
			$(this).dialog('close');
		}
	} );
}