/*!
 * 开发工具栏 - 为传统Web项目提供AI辅助编辑功能
 * 类似stagewise的轻量级实现
 * 仅在开发模式下运行
 */

(function() {
    'use strict';
    
    // 检查是否为开发环境
    var isDevelopment = (function() {
        // 通过URL参数检查开发模式
        var urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('dev') === 'true') return true;
        
        // 通过域名检查
        var hostname = window.location.hostname;
        if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname.includes('dev.') || hostname.includes('staging.')) {
            return true;
        }
        
        return false;
    })();
    
    // 如果不是开发环境，则不加载工具栏
    if (!isDevelopment) {
        return;
    }
    
    var DevToolbar = {
        // 配置
        config: {
            plugins: [],
            position: 'top-right',
            theme: 'dark'
        },
        
        // 状态
        isVisible: false,
        selectedElement: null,
        comments: [],
        
        // 初始化
        init: function(customConfig) {
            this.config = Object.assign(this.config, customConfig || {});
            this.createToolbar();
            this.bindEvents();
            this.loadComments();
            console.log('开发工具栏已初始化');
        },
        
        // 创建工具栏DOM
        createToolbar: function() {
            var toolbarHTML = `
                <div id="dev-toolbar" style="
                    position: fixed;
                    top: 10px;
                    right: 10px;
                    background: ${this.config.theme === 'dark' ? '#333' : '#fff'};
                    border: 1px solid ${this.config.theme === 'dark' ? '#555' : '#ddd'};
                    border-radius: 8px;
                    padding: 10px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                    z-index: 999999;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    font-size: 14px;
                    color: ${this.config.theme === 'dark' ? '#fff' : '#333'};
                    min-width: 280px;
                    max-width: 400px;
                    display: none;
                ">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                        <h4 style="margin: 0; font-size: 16px;">🛠️ 开发工具栏</h4>
                        <button id="toolbar-close" style="
                            background: none;
                            border: none;
                            color: ${this.config.theme === 'dark' ? '#fff' : '#333'};
                            cursor: pointer;
                            font-size: 18px;
                        ">×</button>
                    </div>
                    
                    <div id="element-info" style="margin-bottom: 10px; display: none;">
                        <strong>选中元素:</strong>
                        <div id="element-details" style="
                            background: ${this.config.theme === 'dark' ? '#444' : '#f5f5f5'};
                            padding: 8px;
                            border-radius: 4px;
                            margin-top: 5px;
                            font-family: monospace;
                            font-size: 12px;
                        "></div>
                    </div>
                    
                    <div style="margin-bottom: 10px;">
                        <textarea id="comment-input" placeholder="描述需要修改的内容..." style="
                            width: 100%;
                            height: 80px;
                            border: 1px solid ${this.config.theme === 'dark' ? '#555' : '#ddd'};
                            border-radius: 4px;
                            padding: 8px;
                            background: ${this.config.theme === 'dark' ? '#444' : '#fff'};
                            color: ${this.config.theme === 'dark' ? '#fff' : '#333'};
                            font-family: inherit;
                            font-size: 14px;
                            resize: vertical;
                            box-sizing: border-box;
                        "></textarea>
                    </div>
                    
                    <div style="display: flex; gap: 8px; margin-bottom: 10px;">
                        <button id="add-comment" style="
                            background: #007cba;
                            color: white;
                            border: none;
                            padding: 8px 16px;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 14px;
                            flex: 1;
                        ">添加注释</button>
                        <button id="clear-selection" style="
                            background: #666;
                            color: white;
                            border: none;
                            padding: 8px 16px;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 14px;
                        ">清除选择</button>
                    </div>
                    
                    <div id="comments-list" style="
                        max-height: 200px;
                        overflow-y: auto;
                        border-top: 1px solid ${this.config.theme === 'dark' ? '#555' : '#ddd'};
                        padding-top: 10px;
                    ">
                        <strong>注释列表:</strong>
                        <div id="comments-container"></div>
                    </div>
                    
                    <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid ${this.config.theme === 'dark' ? '#555' : '#ddd'};">
                        <button id="export-comments" style="
                            background: #28a745;
                            color: white;
                            border: none;
                            padding: 6px 12px;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 12px;
                            margin-right: 8px;
                        ">导出注释</button>
                        <button id="toggle-highlight" style="
                            background: #ffc107;
                            color: #333;
                            border: none;
                            padding: 6px 12px;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 12px;
                        ">切换高亮</button>
                    </div>
                </div>
                
                <!-- 浮动切换按钮 -->
                <div id="toolbar-toggle" style="
                    position: fixed;
                    top: 10px;
                    right: 10px;
                    background: #007cba;
                    color: white;
                    border: none;
                    border-radius: 50%;
                    width: 50px;
                    height: 50px;
                    cursor: pointer;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
                    z-index: 999998;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 18px;
                    user-select: none;
                ">🛠️</div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', toolbarHTML);
        },
        
        // 绑定事件
        bindEvents: function() {
            var self = this;
            
            // 切换工具栏显示
            document.getElementById('toolbar-toggle').addEventListener('click', function() {
                self.toggle();
            });
            
            // 关闭工具栏
            document.getElementById('toolbar-close').addEventListener('click', function() {
                self.hide();
            });
            
            // 元素选择
            document.addEventListener('click', function(e) {
                if (self.isVisible && !e.target.closest('#dev-toolbar') && !e.target.closest('#toolbar-toggle')) {
                    e.preventDefault();
                    e.stopPropagation();
                    self.selectElement(e.target);
                }
            }, true);
            
            // 添加注释
            document.getElementById('add-comment').addEventListener('click', function() {
                self.addComment();
            });
            
            // 清除选择
            document.getElementById('clear-selection').addEventListener('click', function() {
                self.clearSelection();
            });
            
            // 导出注释
            document.getElementById('export-comments').addEventListener('click', function() {
                self.exportComments();
            });
            
            // 切换高亮
            document.getElementById('toggle-highlight').addEventListener('click', function() {
                self.toggleHighlight();
            });
            
            // 快捷键
            document.addEventListener('keydown', function(e) {
                // Ctrl+Shift+D 切换工具栏
                if (e.ctrlKey && e.shiftKey && e.key === 'D') {
                    e.preventDefault();
                    self.toggle();
                }
                // ESC 关闭工具栏
                if (e.key === 'Escape' && self.isVisible) {
                    self.hide();
                }
            });
        },
        
        // 显示工具栏
        show: function() {
            document.getElementById('dev-toolbar').style.display = 'block';
            document.getElementById('toolbar-toggle').style.display = 'none';
            this.isVisible = true;
        },
        
        // 隐藏工具栏
        hide: function() {
            document.getElementById('dev-toolbar').style.display = 'none';
            document.getElementById('toolbar-toggle').style.display = 'flex';
            this.isVisible = false;
            this.clearSelection();
        },
        
        // 切换工具栏
        toggle: function() {
            if (this.isVisible) {
                this.hide();
            } else {
                this.show();
            }
        },
        
        // 选择元素
        selectElement: function(element) {
            // 清除之前的选择
            this.clearSelection();
            
            // 设置新选择
            this.selectedElement = element;
            element.style.outline = '2px solid #007cba';
            element.style.backgroundColor = 'rgba(0, 124, 186, 0.1)';
            
            // 显示元素信息
            this.showElementInfo(element);
        },
        
        // 显示元素信息
        showElementInfo: function(element) {
            var info = {
                tagName: element.tagName.toLowerCase(),
                id: element.id || '无',
                className: element.className || '无',
                textContent: element.textContent ? element.textContent.substring(0, 50) + '...' : '无'
            };
            
            document.getElementById('element-details').innerHTML = `
                标签: &lt;${info.tagName}&gt;<br>
                ID: ${info.id}<br>
                Class: ${info.className}<br>
                内容: ${info.textContent}
            `;
            
            document.getElementById('element-info').style.display = 'block';
        },
        
        // 清除选择
        clearSelection: function() {
            if (this.selectedElement) {
                this.selectedElement.style.outline = '';
                this.selectedElement.style.backgroundColor = '';
                this.selectedElement = null;
            }
            document.getElementById('element-info').style.display = 'none';
            document.getElementById('comment-input').value = '';
        },
        
        // 添加注释
        addComment: function() {
            var commentText = document.getElementById('comment-input').value.trim();
            if (!commentText) {
                alert('请输入注释内容');
                return;
            }
            
            if (!this.selectedElement) {
                alert('请先选择一个元素');
                return;
            }
            
            var comment = {
                id: Date.now(),
                element: this.getElementSelector(this.selectedElement),
                text: commentText,
                timestamp: new Date().toLocaleString(),
                elementInfo: {
                    tagName: this.selectedElement.tagName,
                    id: this.selectedElement.id,
                    className: this.selectedElement.className
                }
            };
            
            this.comments.push(comment);
            this.saveComments();
            this.renderComments();
            this.clearSelection();
        },
        
        // 获取元素选择器
        getElementSelector: function(element) {
            var selector = element.tagName.toLowerCase();
            if (element.id) {
                selector += '#' + element.id;
            }
            if (element.className) {
                selector += '.' + element.className.split(' ').join('.');
            }
            return selector;
        },
        
        // 渲染注释列表
        renderComments: function() {
            var container = document.getElementById('comments-container');
            container.innerHTML = '';
            
            if (this.comments.length === 0) {
                container.innerHTML = '<div style="color: #666; font-style: italic;">暂无注释</div>';
                return;
            }
            
            this.comments.forEach(function(comment, index) {
                var commentEl = document.createElement('div');
                commentEl.style.cssText = `
                    background: ${this.config.theme === 'dark' ? '#444' : '#f9f9f9'};
                    border: 1px solid ${this.config.theme === 'dark' ? '#555' : '#ddd'};
                    border-radius: 4px;
                    padding: 8px;
                    margin-bottom: 8px;
                    font-size: 12px;
                `;
                
                commentEl.innerHTML = `
                    <div style="font-weight: bold; margin-bottom: 4px;">
                        ${comment.element}
                    </div>
                    <div style="margin-bottom: 4px;">
                        ${comment.text}
                    </div>
                    <div style="color: #666; font-size: 11px;">
                        ${comment.timestamp}
                        <button onclick="DevToolbar.removeComment(${comment.id})" style="
                            float: right;
                            background: #dc3545;
                            color: white;
                            border: none;
                            padding: 2px 6px;
                            border-radius: 2px;
                            cursor: pointer;
                            font-size: 10px;
                        ">删除</button>
                    </div>
                `;
                
                container.appendChild(commentEl);
            }.bind(this));
        },
        
        // 删除注释
        removeComment: function(commentId) {
            this.comments = this.comments.filter(function(comment) {
                return comment.id !== commentId;
            });
            this.saveComments();
            this.renderComments();
        },
        
        // 保存注释到localStorage
        saveComments: function() {
            try {
                localStorage.setItem('dev-toolbar-comments', JSON.stringify(this.comments));
            } catch (e) {
                console.warn('无法保存注释到localStorage');
            }
        },
        
        // 从localStorage加载注释
        loadComments: function() {
            try {
                var saved = localStorage.getItem('dev-toolbar-comments');
                if (saved) {
                    this.comments = JSON.parse(saved);
                    this.renderComments();
                }
            } catch (e) {
                console.warn('无法从localStorage加载注释');
                this.comments = [];
            }
        },
        
        // 导出注释
        exportComments: function() {
            if (this.comments.length === 0) {
                alert('没有注释可导出');
                return;
            }
            
            var exportData = {
                url: window.location.href,
                timestamp: new Date().toISOString(),
                comments: this.comments
            };
            
            var blob = new Blob([JSON.stringify(exportData, null, 2)], {
                type: 'application/json'
            });
            
            var url = URL.createObjectURL(blob);
            var a = document.createElement('a');
            a.href = url;
            a.download = 'dev-comments-' + new Date().toISOString().slice(0, 10) + '.json';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        },
        
        // 切换高亮模式
        toggleHighlight: function() {
            var style = document.getElementById('dev-highlight-style');
            if (style) {
                style.remove();
            } else {
                var highlightStyle = document.createElement('style');
                highlightStyle.id = 'dev-highlight-style';
                highlightStyle.textContent = `
                    * {
                        outline: 1px solid rgba(255, 0, 0, 0.3) !important;
                    }
                    *:hover {
                        outline: 2px solid rgba(255, 0, 0, 0.6) !important;
                        background-color: rgba(255, 0, 0, 0.1) !important;
                    }
                `;
                document.head.appendChild(highlightStyle);
            }
        }
    };
    
    // 全局暴露
    window.DevToolbar = DevToolbar;
    
    // 自动初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            DevToolbar.init();
        });
    } else {
        DevToolbar.init();
    }
    
})(); 