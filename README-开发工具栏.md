# 开发工具栏使用说明

## 概述

此项目为传统PHP/jQuery Web应用实现了一个类似stagewise的轻量级开发工具栏，提供AI辅助编辑能力。该工具栏仅在开发模式下运行，不会影响生产环境。

## 功能特性

### 🛠️ 核心功能
- **元素选择**: 点击页面任意元素进行选择和标记
- **注释系统**: 为选中元素添加修改需求注释
- **数据持久化**: 注释自动保存到localStorage
- **数据导出**: 将注释导出为JSON格式文件
- **高亮模式**: 切换页面元素边框高亮
- **快捷键支持**: 快速切换工具栏显示/隐藏

### 🎯 使用场景
- 前端UI迭代和修改需求记录
- 设计师与开发者之间的沟通
- Bug跟踪和功能请求
- 代码审查和改进建议

## 安装与配置

### 1. 文件结构
```
ebookshelf/
├── js/
│   └── dev-toolbar.js          # 开发工具栏核心文件
└── books/jimp-1a/
    ├── index.php               # 生产版本(原始文件)
    └── index-dev.php           # 开发版本(集成工具栏)
```

### 2. 开发模式激活

#### 方法一: URL参数
```
http://your-domain.com/ebookshelf/books/jimp-1a/index-dev.php?dev=true
```

#### 方法二: 域名检测
工具栏会自动在以下域名环境下激活：
- `localhost`
- `127.0.0.1`
- `dev.your-domain.com`
- `staging.your-domain.com`

#### 方法三: 快捷键
按 `Ctrl+Shift+D` 可随时切换工具栏显示

## 使用方法

### 基本操作流程

#### 1. 启动开发模式
- 访问 `index-dev.php?dev=true`
- 页面左上角会显示 "🛠️ 开发模式" 标识
- 右上角出现蓝色工具栏切换按钮

#### 2. 选择元素
- 点击工具栏切换按钮（🛠️）打开工具栏
- 点击页面任意元素进行选择
- 选中元素会显示蓝色边框和背景高亮
- 工具栏会显示元素信息（标签名、ID、Class等）

#### 3. 添加注释
- 在文本框中输入修改需求或意见
- 点击"添加注释"按钮保存
- 注释会自动保存到浏览器localStorage

#### 4. 管理注释
- 查看所有已添加的注释列表
- 点击"删除"按钮移除特定注释
- 使用"导出注释"功能下载JSON文件

### 高级功能

#### 高亮模式
- 点击"切换高亮"按钮
- 页面所有元素显示红色边框
- 便于快速识别页面结构

#### 快捷键操作
- `Ctrl+Shift+D`: 切换工具栏显示/隐藏
- `ESC`: 关闭工具栏

#### 数据导出格式
```json
{
  "url": "http://example.com/page",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "comments": [
    {
      "id": 1704067200000,
      "element": "div.zoom-icon.zoom-icon-in",
      "text": "放大按钮需要改成渐进式缩放",
      "timestamp": "2024/1/1 上午8:00:00",
      "elementInfo": {
        "tagName": "DIV",
        "id": "",
        "className": "zoom-icon zoom-icon-in"
      }
    }
  ]
}
```

## 技术实现

### 环境检测
```javascript
// 开发环境自动检测
var isDevelopment = (function() {
    // URL参数检查
    var urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('dev') === 'true') return true;
    
    // 域名检查
    var hostname = window.location.hostname;
    if (hostname === 'localhost' || 
        hostname === '127.0.0.1' || 
        hostname.includes('dev.') || 
        hostname.includes('staging.')) {
        return true;
    }
    
    return false;
})();
```

### PHP集成
```php
// 服务器端开发模式检测
$isDevelopment = false;
if (isset($_GET['dev']) && $_GET['dev'] === 'true') {
    $isDevelopment = true;
}

// 条件加载开发工具栏
<?php if ($isDevelopment): ?>
<script type="text/javascript" src="../../js/dev-toolbar.js"></script>
<?php endif; ?>
```

## 最佳实践

### 1. 团队协作
- 建立统一的注释格式规范
- 定期导出和整理注释数据
- 使用版本控制管理导出的JSON文件

### 2. 注释质量
- 描述具体的修改需求，而非仅指出问题
- 包含预期效果和实现建议
- 提供相关设计图或参考链接

### 3. 数据管理
- 定期清理无用注释
- 按项目模块分类整理导出数据
- 建立注释处理状态跟踪机制

## 安全考虑

### 生产环境隔离
- 工具栏仅在开发模式下加载
- 生产环境使用原始 `index.php` 文件
- 无开发模式标识时工具栏自动禁用

### 数据安全
- 注释数据仅存储在客户端localStorage
- 不会向服务器发送敏感信息
- 可通过清除浏览器数据删除所有注释

## 扩展功能

### 自定义配置
```javascript
// 初始化时可传入配置选项
DevToolbar.init({
    theme: 'light',      // 'dark' | 'light'
    position: 'top-left', // 工具栏位置
    plugins: []          // 扩展插件
});
```

### 与AI系统集成
导出的JSON数据可以直接提供给AI代码助手，用于：
- 自动生成代码修改建议
- 批量处理UI调整需求
- 生成修改任务清单

## 故障排除

### 常见问题

#### 工具栏不显示
1. 检查是否正确访问 `index-dev.php?dev=true`
2. 确认浏览器控制台无JavaScript错误
3. 验证 `dev-toolbar.js` 文件路径是否正确

#### 注释无法保存
1. 检查浏览器是否支持localStorage
2. 确认localStorage配额未超限
3. 尝试清除浏览器缓存后重试

#### 元素选择不准确
1. 确认页面CSS样式未干扰工具栏样式
2. 检查是否有其他JavaScript事件冲突
3. 尝试使用高亮模式确认元素边界

### 浏览器兼容性
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持元素选择和注释功能
- 实现数据持久化和导出
- 添加快捷键支持
- 集成开发模式检测

---

**注意**: 此工具栏是stagewise的轻量级替代方案，专为传统Web项目设计。完整的stagewise功能需要现代前端框架和Node.js环境支持。 