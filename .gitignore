# ==============================================
# macOS 系统文件
# ==============================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ==============================================
# PHP 相关文件
# ==============================================
# Composer
vendor/
composer.lock
composer.phar

# PHP 日志文件
*.log
error_log
access_log

# PHP 配置文件（包含敏感信息）
config/database.php
config/secrets.php
.env
.env.local
.env.production
.env.staging

# PHP 会话文件
/tmp/
session/
sessions/

# PHP 缓存
cache/
temp/
tmp/

# PHP 上传文件目录（根据需要调整）
uploads/private/
uploads/temp/

# ==============================================
# 开发工具和编辑器
# ==============================================
# IDE 配置文件
.vscode/
.idea/
*.swp
*.swo
*~

# Cursor 编辑器
.cursor/

# ==============================================
# 前端开发相关
# ==============================================
# Node.js（如果使用）
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json

# 构建输出
dist/
build/
*.min.js.map
*.min.css.map

# ==============================================
# 数据库相关
# ==============================================
# SQLite 数据库
*.sqlite
*.db
*.sqlite3

# MySQL dump 文件
*.sql
dump.sql

# ==============================================
# 媒体文件和内容
# ==============================================
# 大型媒体文件（根据项目需要调整）
*.mp4
*.avi
*.mov
*.wmv
*.flv

# 大型图片文件（超过一定大小的）
# 注意：如果这些是项目必需的，请从忽略列表中移除
*.psd
*.ai

# PDF 文件（如果是动态生成的）
/ebookshelf/pdf/generated/
/ebookshelf/pdf/temp/

# ==============================================
# 服务器和部署相关
# ==============================================
# Apache/Nginx 配置
.htaccess.backup
.htpasswd

# 备份文件
*.backup
*.bak
*.old
*~
*.orig

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# ==============================================
# 安全和隐私
# ==============================================
# 密钥文件
*.key
*.pem
*.crt
private_key*
public_key*

# 用户数据备份
user_data/
backups/

# ==============================================
# 项目特定
# ==============================================
# 书籍内容（如果是版权内容）
/ebookshelf/books/copyrighted/
/ebookshelf/cover/private/

# 用户上传的内容
/ebookshelf/user_uploads/

# 测试文件
test_*.php
debug_*.php
*_test.php

# 文档生成
docs/generated/ 