# 电子书阅读器功能改版需求文档

## 项目概述

本项目是一个基于Web的电子书阅读器系统，使用了Turn.js库实现翻页效果，LiterallyCanvas库实现画笔功能，以及自定义的缩放功能。当前需要对三个核心功能进行改版升级。

## 当前技术架构分析

### 核心技术栈
- **前端框架**: jQuery + Turn.js (翻页) + LiterallyCanvas (画笔)
- **缩放实现**: 自定义zoom.js插件
- **UI组件**: jQuery UI Dialog
- **CSS**: 响应式布局 + CSS3 Transform

### 现有功能模块
1. **翻页系统**: Turn.js实现的书籍翻页效果
2. **缩放系统**: 基于zoom.js的三级缩放(1x, 1.5x, 2x, 2.5x)
3. **画笔系统**: LiterallyCanvas实现的绘画功能
4. **响应式**: 基于CSS Transform的设备适配

## 需求改版详情

### 需求1: 渐进式缩放功能改版

#### 1.1 现状分析
**当前实现位置**: 
- `ebookshelf/js/magazine.js` (第60-91行)
- `ebookshelf/js/loadapp.js` (第1200-1230行)

**现状问题**:
```javascript
// 当前固定三级缩放实现
if ($('.magazine-viewport').zoom('value')==1) {
    zoomMax = 1.5;
    $('.magazine-viewport').zoom('zoomIn', event);
} else if ($('.magazine-viewport').zoom('value')==1.5) {
    $('.magazine-viewport').zoom('zoomOut',0);
    zoomMax = 2;
    $('.magazine-viewport').zoom('zoomIn', event);
} else if ($('.magazine-viewport').zoom('value')==2) {
    $('.magazine-viewport').zoom('zoomOut',0);
    zoomMax = 2.5;
    $('.magazine-viewport').zoom('zoomIn', event);
} else {
    $('.magazine-viewport').zoom('zoomOut');
}
```

#### 1.2 改版目标
- **缩放范围**: 0.5x - 3.0x (可配置)
- **缩放步长**: 0.1x (可配置)
- **动画效果**: CSS3 transition实现平滑渐进式缩放
- **交互方式**: 
  - 鼠标滚轮缩放
  - 触摸手势缩放(双指)
  - 缩放按钮点击
  - 双击缩放

#### 1.3 技术实现方案

**1.3.1 修改zoom.js配置**
```javascript
// 新的缩放配置
var zoomOptions = {
    min: 0.5,           // 最小缩放比例
    max: 3.0,           // 最大缩放比例
    step: 0.1,          // 缩放步长
    duration: 300,      // 动画持续时间(ms)
    easing: 'ease-out', // 缓动函数
    wheelSensitivity: 0.1, // 滚轮灵敏度
    touchSensitivity: 0.01 // 触摸灵敏度
};
```

**1.3.2 新增交互事件处理**
```javascript
// 鼠标滚轮缩放
$('.magazine-viewport').on('wheel', function(e) {
    e.preventDefault();
    var delta = e.originalEvent.deltaY;
    var currentZoom = $(this).zoom('value');
    var newZoom = delta > 0 ? 
        Math.max(zoomOptions.min, currentZoom - zoomOptions.step) :
        Math.min(zoomOptions.max, currentZoom + zoomOptions.step);
    $(this).zoom('setZoom', newZoom);
});

// 触摸手势缩放
var hammer = new Hammer($('.magazine-viewport')[0]);
hammer.get('pinch').set({ enable: true });
hammer.on('pinch', function(e) {
    var newZoom = Math.min(zoomOptions.max, 
        Math.max(zoomOptions.min, e.scale));
    $('.magazine-viewport').zoom('setZoom', newZoom);
});
```

**1.3.3 UI组件改版**
- 移除现有的固定缩放按钮
- 新增缩放滑块控件
- 新增缩放比例显示
- 新增重置缩放按钮

#### 1.4 文件修改清单
1. `ebookshelf/js/zoom.js` - 核心缩放逻辑重构
2. `ebookshelf/js/magazine.js` - zoomTo函数重写
3. `ebookshelf/js/loadapp.js` - 缩放按钮事件处理修改
4. `ebookshelf/css/magazine.css` - 缩放UI样式更新
5. `ebookshelf/books/*/index.php` - HTML结构调整

### 需求2: 画笔功能兼容性改版

#### 2.1 现状分析
**当前实现位置**:
- `ebookshelf/js/loadapp.js` (第1290-1380行)
- `ebookshelf/js/literallycanvas-core.js`

**现状问题**:
```javascript
// 当前画笔功能只在1x缩放下可用
if ( $('.magazine-viewport').zoom("value")==1 ) {
    // 只有在未缩放状态下才能开启画笔
    closeDialog();
    // ... 画笔初始化代码
}
```

#### 2.2 改版目标
- **全缩放兼容**: 在任意缩放比例下都能正常使用画笔
- **坐标精确**: 画笔坐标与实际显示位置精确对应
- **性能优化**: 缩放状态下画笔响应流畅
- **UI适配**: 画笔工具栏在不同缩放下正确显示

#### 2.3 技术实现方案

**2.3.1 坐标系统重构**
```javascript
// 新的坐标转换函数
function getCanvasCoordinates(clientX, clientY) {
    var viewport = $('.magazine-viewport');
    var currentZoom = viewport.zoom('value');
    var offset = $('.magazine').offset();
    var scrollPos = viewport.zoom('getScrollPosition');
    
    return {
        x: (clientX - offset.left + scrollPos.x) / currentZoom,
        y: (clientY - offset.top + scrollPos.y) / currentZoom
    };
}

// 画布尺寸动态调整
function adjustCanvasSize() {
    var currentZoom = $('.magazine-viewport').zoom('value');
    var baseWidth = flipBookSingleWidth;
    var baseHeight = flipBookHeight;
    
    lc.setImageSize(baseWidth, baseHeight);
    lc.setZoom(currentZoom);
}
```

**2.3.2 数据保存兼容性处理**
```javascript
// 关键：保持数据库中保存的笔刷数据格式不变
// 现有保存逻辑：saveCanvas(type, id, imageData, snapshotJSON)
// - imageData: lc.getImage().toDataURL() 
// - snapshotJSON: JSON.stringify(lc.getSnapshot())

// 数据保存时的坐标标准化
function saveDrawingData() {
    var currentPage = $(".magazine").turn("page");
    var currentZoom = $('.magazine-viewport').zoom('value');
    
    // 重要：将坐标还原到1x标准尺寸保存，确保数据库兼容性
    var normalizedSnapshot = normalizeSnapshotCoordinates(lc.getSnapshot(), currentZoom);
    
    if ($(".p" + currentPage).find('.custom-draw').length == 1) {
        var id = $(".p" + currentPage).find('.custom-draw').attr('region-id');
        if (normalizedSnapshot.shapes.length > 0) {
            // 保存标准化后的坐标数据
            saveCanvas(2, id, lc.getImage().toDataURL(), JSON.stringify(normalizedSnapshot));
        } else {
            saveCanvas(3, id, '', '');
        }
    } else {
        if (normalizedSnapshot.shapes.length > 0) {
            saveCanvas(1, 0, lc.getImage().toDataURL(), JSON.stringify(normalizedSnapshot));
        }
    }
}

// 坐标标准化函数
function normalizeSnapshotCoordinates(snapshot, currentZoom) {
    var normalizedSnapshot = JSON.parse(JSON.stringify(snapshot)); // 深拷贝
    
    // 将所有shape的坐标除以缩放比例，还原到标准尺寸
    normalizedSnapshot.shapes = normalizedSnapshot.shapes.map(function(shape) {
        if (shape.className === 'LinePath') {
            // 处理画笔路径点坐标
            shape.data.points = shape.data.points.map(function(point) {
                return {
                    ...point,
                    x: point.x / currentZoom,
                    y: point.y / currentZoom
                };
            });
        } else if (shape.className === 'Line') {
            // 处理直线坐标
            shape.data.x1 = shape.data.x1 / currentZoom;
            shape.data.y1 = shape.data.y1 / currentZoom;
            shape.data.x2 = shape.data.x2 / currentZoom;
            shape.data.y2 = shape.data.y2 / currentZoom;
        }
        // 其他shape类型的坐标处理...
        return shape;
    });
    
    return normalizedSnapshot;
}

// 数据加载时的坐标缩放适配
function loadDrawingData(snapshotJSON) {
    var currentZoom = $('.magazine-viewport').zoom('value');
    var snapshot = JSON.parse(snapshotJSON);
    
    // 将保存的标准坐标按当前缩放比例进行调整
    var scaledSnapshot = scaleSnapshotCoordinates(snapshot, currentZoom);
    
    lc.loadSnapshot(scaledSnapshot);
}

// 坐标缩放函数
function scaleSnapshotCoordinates(snapshot, currentZoom) {
    var scaledSnapshot = JSON.parse(JSON.stringify(snapshot)); // 深拷贝
    
    // 将所有shape的坐标乘以缩放比例
    scaledSnapshot.shapes = scaledSnapshot.shapes.map(function(shape) {
        if (shape.className === 'LinePath') {
            shape.data.points = shape.data.points.map(function(point) {
                return {
                    ...point,
                    x: point.x * currentZoom,
                    y: point.y * currentZoom
                };
            });
        } else if (shape.className === 'Line') {
            shape.data.x1 = shape.data.x1 * currentZoom;
            shape.data.y1 = shape.data.y1 * currentZoom;
            shape.data.x2 = shape.data.x2 * currentZoom;
            shape.data.y2 = shape.data.y2 * currentZoom;
        }
        return shape;
    });
    
    return scaledSnapshot;
}
```

**2.3.3 LiterallyCanvas坐标系统集成**
```javascript
// 利用现有的clientCoordsToDrawingCoords方法
// 该方法已经处理了基本的坐标转换：
// return {
//     x: (x * this.backingScale - this.position.x) / this.getRenderScale(),
//     y: (y * this.backingScale - this.position.y) / this.getRenderScale()
// };

// 重写LiterallyCanvas的setZoom方法，确保与外部缩放系统同步
LC.prototype.setZoom = function(zoomLevel, offsetX, offsetY) {
    // 调用原有zoom方法
    this.zoom(zoomLevel, offsetX, offsetY);
    
    // 同步外部缩放状态
    $('.magazine-viewport').zoom('setZoom', zoomLevel);
};
```

**2.3.4 画笔工具栏适配**
```javascript
// 工具栏位置动态调整
function adjustToolbarPosition() {
    var viewport = $('.magazine-viewport');
    var currentZoom = viewport.zoom('value');
    var magazineOffset = $('.magazine').offset();
    
    $('#lc').css({
        'transform': `scale(${currentZoom})`,
        'transform-origin': 'top left',
        'left': magazineOffset.left,
        'top': magazineOffset.top
    });
}
```

**2.3.5 事件处理优化**
```javascript
// 移除缩放限制
$('.highlighter-icon').bind('click touchstart', function() {
    // 移除原有的缩放检查
    // if ( $('.magazine-viewport').zoom("value")==1 ) { ... }
    
    closeDialog();
    initDrawingMode();
});

function initDrawingMode() {
    var currentPage = $(".magazine").turn("page");
    var currentZoom = $('.magazine-viewport').zoom('value');
    
    // 动态计算画布尺寸和位置
    adjustCanvasSize();
    adjustToolbarPosition();
    
    // 初始化画笔工具
    initLiterallyCanvas();
}
```

#### 2.4 UI改版方案

**2.4.1 工具栏重新设计**
- 工具栏改为浮动式设计，不受缩放影响
- 新增缩放感知的笔刷大小调整
- 优化触摸设备上的工具选择体验

**2.4.2 视觉反馈优化**
- 新增画笔预览功能
- 实时显示当前笔刷大小和颜色
- 缩放时保持画笔相对大小一致

#### 2.5 文件修改清单
1. `ebookshelf/js/loadapp.js` - 画笔初始化逻辑重构，数据保存/加载逻辑改造
2. `ebookshelf/js/literallycanvas-core.js` - 坐标系统适配
3. `ebookshelf/js/magazine.js` - saveCanvas函数调用点更新
4. `ebookshelf/js/drawing-data-adapter.js` - 新增数据兼容性处理模块
5. `ebookshelf/css/literallycanvas.css` - 工具栏样式更新
6. `ebookshelf/css/magazine.css` - 画笔模式样式调整
7. `ebookshelf/load/canvas_save.php` - 数据库保存逻辑检查（可能需要兼容性验证）

### 需求3: 设备分辨率自适应改版

#### 3.1 现状分析
**当前实现位置**:
- `ebookshelf/html5/*/js/*.js` (各HTML5组件)
- `ebookshelf/js/loadapp.js` (对话框尺寸调整)

**现状问题**:
```javascript
// 当前简单的缩放适配
var zoomLevel1 = $(window).width() / $('body').width();
var zoomLevel2 = $(window).height() / $('body').height();
var zoomLevel = Math.min(zoomLevel1, zoomLevel2);
$('body').css({
    'transform': 'scale('+zoomLevel+', '+zoomLevel+')',
});
```

#### 3.2 改版目标
- **设备检测**: 自动识别电脑和iPad设备
- **分辨率适配**: 根据设备分辨率优化显示效果
- **布局优化**: 不同设备采用不同的布局策略
- **性能优化**: 根据设备性能调整渲染质量

#### 3.3 技术实现方案

**3.3.1 设备检测系统**
```javascript
// 设备检测工具类
class DeviceDetector {
    static detect() {
        const userAgent = navigator.userAgent.toLowerCase();
        const screen = window.screen;
        
        return {
            type: this.getDeviceType(userAgent, screen),
            resolution: this.getResolution(),
            pixelRatio: window.devicePixelRatio || 1,
            orientation: this.getOrientation(),
            touchSupport: 'ontouchstart' in window
        };
    }
    
    static getDeviceType(userAgent, screen) {
        if (/ipad/.test(userAgent)) return 'ipad';
        if (/tablet/.test(userAgent)) return 'tablet';
        if (screen.width >= 1024 && screen.height >= 768) return 'desktop';
        return 'mobile';
    }
    
    static getResolution() {
        return {
            width: window.screen.width,
            height: window.screen.height,
            availWidth: window.screen.availWidth,
            availHeight: window.screen.availHeight
        };
    }
}
```

**3.3.2 自适应布局系统**
```javascript
// 自适应配置
const adaptiveConfig = {
    desktop: {
        bookSize: { width: 1562, height: 1000 },
        minZoom: 0.5,
        maxZoom: 3.0,
        toolbarSize: 'large',
        quality: 'high'
    },
    ipad: {
        bookSize: { width: 1024, height: 768 },
        minZoom: 0.8,
        maxZoom: 2.0,
        toolbarSize: 'medium',
        quality: 'medium'
    },
    tablet: {
        bookSize: { width: 800, height: 600 },
        minZoom: 1.0,
        maxZoom: 1.5,
        toolbarSize: 'small',
        quality: 'medium'
    }
};

// 自适应初始化
function initAdaptiveLayout() {
    const device = DeviceDetector.detect();
    const config = adaptiveConfig[device.type];
    
    // 应用设备特定配置
    applyDeviceConfig(config, device);
    
    // 监听方向变化
    window.addEventListener('orientationchange', () => {
        setTimeout(() => {
            updateLayoutForOrientation();
        }, 100);
    });
}
```

**3.3.3 响应式书籍尺寸**
```javascript
// 动态书籍尺寸计算
function calculateOptimalBookSize(device) {
    const viewport = {
        width: window.innerWidth,
        height: window.innerHeight
    };
    
    const config = adaptiveConfig[device.type];
    let bookWidth, bookHeight;
    
    if (device.orientation === 'landscape') {
        // 横屏模式
        bookWidth = Math.min(viewport.width * 0.9, config.bookSize.width);
        bookHeight = bookWidth * (config.bookSize.height / config.bookSize.width);
    } else {
        // 竖屏模式
        bookHeight = Math.min(viewport.height * 0.8, config.bookSize.height);
        bookWidth = bookHeight * (config.bookSize.width / config.bookSize.height);
    }
    
    return { width: bookWidth, height: bookHeight };
}
```

**3.3.4 性能优化策略**
```javascript
// 根据设备性能调整渲染质量
function applyPerformanceOptimizations(device) {
    if (device.type === 'ipad' || device.type === 'tablet') {
        // 移动设备优化
        $('.magazine').addClass('mobile-optimized');
        
        // 降低动画复杂度
        $.fx.interval = 50;
        
        // 启用硬件加速
        $('.magazine-viewport').css({
            'transform': 'translateZ(0)',
            'backface-visibility': 'hidden'
        });
    }
}
```

#### 3.4 CSS媒体查询增强
```css
/* 设备特定样式 */
@media screen and (max-width: 1024px) {
    .magazine-viewport {
        padding: 10px;
    }
    
    .zoom-icon, .page-icon {
        width: 60px;
        height: 60px;
    }
}

@media screen and (orientation: portrait) and (max-width: 768px) {
    .magazine {
        transform-origin: top center;
    }
    
    .bottom {
        position: fixed;
        bottom: 0;
    }
}

/* iPad特定优化 */
@media screen and (device-width: 768px) and (device-height: 1024px) {
    .magazine-viewport {
        max-width: 90vw;
        max-height: 85vh;
    }
}
```

#### 3.5 文件修改清单
1. `ebookshelf/js/device-detector.js` - 新增设备检测模块
2. `ebookshelf/js/adaptive-layout.js` - 新增自适应布局模块
3. `ebookshelf/js/loadapp.js` - 集成自适应系统
4. `ebookshelf/css/adaptive.css` - 新增自适应样式
5. `ebookshelf/css/magazine.css` - 更新响应式样式
6. 所有`ebookshelf/books/*/index.php` - 更新HTML结构

## 实施计划

### 阶段一: 渐进式缩放功能 (预计2周)
1. **第1-3天**: zoom.js核心逻辑重构
2. **第4-6天**: 新增交互事件处理
3. **第7-9天**: UI组件重新设计
4. **第10-12天**: 测试和优化
5. **第13-14天**: 文档更新和代码审查

### 阶段二: 画笔功能兼容性 (预计1.5周)
1. **第1-3天**: 坐标系统重构
2. **第4-6天**: 工具栏适配和UI改版
3. **第7-8天**: 事件处理优化
4. **第9-10天**: 测试和性能优化

### 阶段三: 设备分辨率自适应 (预计2周)
1. **第1-3天**: 设备检测系统开发
2. **第4-7天**: 自适应布局系统实现
3. **第8-10天**: CSS媒体查询优化
4. **第11-12天**: 性能优化和测试
5. **第13-14天**: 整体集成测试

### 阶段四: 整体测试和优化 (预计1周)
1. **第1-3天**: 跨设备兼容性测试
2. **第4-5天**: 性能测试和优化
3. **第6-7天**: 用户体验测试和调整

## 技术风险评估

### 高风险项
1. **缩放功能重构**: 可能影响现有翻页效果
2. **坐标系统改动**: 可能导致画笔定位不准确
3. **设备兼容性**: 不同设备表现可能不一致
4. **数据库兼容性**: 已保存的笔刷数据可能在新缩放系统下显示异常

### 风险缓解措施
1. **渐进式开发**: 分阶段实施，每个阶段充分测试
2. **向后兼容**: 保留原有功能作为备选方案
3. **设备测试**: 在目标设备上进行充分测试
4. **性能监控**: 实时监控性能指标
5. **数据迁移测试**: 
   - 建立测试数据集，包含各种缩放比例下的笔刷数据
   - 验证坐标转换算法的准确性
   - 制定数据回滚策略
6. **分离式部署**: 新功能可开关，出现问题时快速回退到原系统

## 验收标准

### 功能验收
1. **缩放功能**: 0.2x-4.0x范围内平滑缩放，动画流畅
2. **画笔功能**: 任意缩放比例下都能正常使用，坐标精确
3. **设备适配**: 电脑和iPad上显示效果优良，性能稳定
4. **数据兼容性**: 
   - 原有笔刷数据在新系统中正确显示
   - 新系统保存的数据在各缩放比例下一致
   - 数据库中的坐标数据保持标准化格式

### 性能验收
1. **响应时间**: 缩放操作响应时间<100ms
2. **内存使用**: 相比原版内存使用增长<20%
3. **兼容性**: 支持Chrome、Safari、Firefox主流浏览器
4. **数据处理**: 坐标转换处理时间<50ms，不影响绘画流畅度

### 用户体验验收
1. **操作直观**: 用户无需学习即可使用新功能
2. **视觉一致**: 不同设备上保持一致的视觉体验
3. **稳定性**: 连续使用2小时无明显性能下降
4. **数据可靠性**: 画笔数据保存成功率>99.9%，无数据丢失

## 后续维护建议

1. **定期更新**: 根据新设备和浏览器更新适配代码
2. **性能监控**: 建立性能监控体系，及时发现问题
3. **用户反馈**: 收集用户使用反馈，持续优化体验
4. **技术升级**: 关注相关技术发展，适时进行技术升级